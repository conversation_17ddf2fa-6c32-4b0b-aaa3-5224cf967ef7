# 日志分析系统

一个基于Spring Boot的日志分析系统，用于解析、存储和可视化大型日志文件数据。

## 功能特性

- 支持解析JSON格式的日志文件
- 批量导入多个2GB大小的日志文件
- 支持多机器日志区分
- PostgreSQL数据库存储，支持JSONB字段
- 实时统计和图表展示
- 基于FreeMarker的Web界面
- Chart.js图表可视化

## 技术栈

- **后端**: Spring Boot 2.6.13, MyBatis, PostgreSQL
- **前端**: FreeMarker, Chart.js, HTML/CSS/JavaScript
- **数据库**: PostgreSQL 12+
- **构建工具**: Maven

## 日志格式

系统支持以下格式的日志：

```
[2025-07-01 15:02:23.276] {"path":"/api/project/skill/performTicketOrder","ajax":true,"status":1,"errorCode":"","errorMsg":"","time":156,"startTime":"2025-07-01 15:02:23.120","endTime":"2025-07-01 15:02:23.276","className":"com.marry.suchao.controller.ProjectController","method":"performTicketOrder","param":{"ip":"*************","netUserId":12345,"orderId":"ORD001","amount":99.99},"comments":"订单处理成功"}
```

### 字段说明

- **errorMsg**: 主要的错误消息字段
- **comments**: 备用的注释字段，当errorMsg为空时会自动使用comments作为错误消息
- **param**: JSON格式的参数对象，会完整存储并提取常用字段（ip、netUserId等）

## 快速开始

### 1. 环境准备

确保您的系统已安装：
- Java 11+
- Maven 3.6+
- PostgreSQL 12+

### 2. 数据库配置

1. 创建PostgreSQL数据库：
```sql
CREATE DATABASE suchaologanalysis;
```

2. 修改 `src/main/resources/application.properties` 中的数据库连接信息：
```properties
spring.datasource.url=**************************************************
spring.datasource.username=your_username
spring.datasource.password=your_password
```

3. 运行数据库初始化脚本：
```sql
-- 执行 src/main/resources/schema.sql 中的SQL语句
```

### 3. 编译和运行

```bash
# 编译项目
mvn clean compile

# 运行应用
mvn spring-boot:run
```

应用将在 http://localhost:8080 启动。

### 4. 使用系统

1. **访问首页**: http://localhost:8080/log/
2. **导入日志**: http://localhost:8080/log/import
3. **查看统计**: 在首页选择时间范围和机器名称查看图表

## 主要功能

### 日志导入

- **文件上传**: 通过Web界面上传日志文件
- **批量导入**: 支持本地文件路径批量导入
- **机器区分**: 为每个日志文件指定机器名称

### 数据统计

- **时间趋势**: 按小时/天统计请求量
- **响应时间分布**: 不同响应时间区间的请求分布
- **错误统计**: 错误类型和数量统计
- **路径统计**: 按API路径统计访问量和性能指标
- **机器对比**: 支持按机器名称过滤统计

### 可视化图表

- 折线图：请求量时间趋势
- 饼图：响应时间分布
- 柱状图：错误统计
- 水平柱状图：API路径访问统计（Top 20）

## API接口

### 统计接口

- `GET /log/statistics/hourly` - 按小时统计
- `GET /log/statistics/daily` - 按天统计
- `GET /log/statistics/errors` - 错误统计
- `GET /log/statistics/response-time` - 响应时间统计
- `GET /log/statistics/path` - 按API路径统计

### 管理接口

- `POST /log/upload` - 上传日志文件
- `POST /log/import-local` - 批量导入本地文件
- `POST /log/clear` - 清空日志数据

## 数据库表结构

```sql
CREATE TABLE log_records (
    id BIGSERIAL PRIMARY KEY,
    timestamp TIMESTAMP NOT NULL,
    path VARCHAR(500),
    ajax BOOLEAN,
    status INTEGER,
    error_code VARCHAR(50),
    error_msg TEXT,
    time BIGINT,
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    class_name VARCHAR(500),
    method VARCHAR(200),
    param JSONB,
    ip VARCHAR(50),
    net_user_id BIGINT,
    machine_name VARCHAR(100) NOT NULL,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 性能优化

- 使用批量插入提高导入性能
- 创建适当的数据库索引
- JSONB字段支持高效JSON查询
- 连接池配置优化

## 注意事项

1. 确保PostgreSQL服务正在运行
2. 大文件导入可能需要较长时间，请耐心等待
3. 建议定期清理旧的日志数据以保持性能
4. 生产环境请修改默认的数据库密码

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查PostgreSQL服务状态
   - 验证数据库连接信息
   - 确认数据库用户权限

2. **文件导入失败**
   - 检查日志格式是否正确
   - 确认文件编码为UTF-8
   - 查看应用日志获取详细错误信息

3. **图表不显示**
   - 检查浏览器控制台错误
   - 确认时间范围内有数据
   - 验证网络连接

## 开发说明

### 项目结构

```
src/main/java/com/marry/suchaologanalysis/
├── controller/          # 控制器层
├── service/            # 服务层
├── mapper/             # 数据访问层
├── entity/             # 实体类
└── SuchaoLogAnalysisApplication.java

src/main/resources/
├── mappers/            # MyBatis映射文件
├── templates/          # FreeMarker模板
├── application.properties
└── schema.sql
```

### 扩展功能

- 添加更多统计维度
- 支持实时日志监控
- 增加告警功能
- 导出统计报表

## 许可证

本项目采用 MIT 许可证。
