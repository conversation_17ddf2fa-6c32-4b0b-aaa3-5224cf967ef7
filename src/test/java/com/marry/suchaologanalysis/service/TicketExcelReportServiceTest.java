package com.marry.suchaologanalysis.service;

import com.marry.suchaologanalysis.dto.TicketStatisticsDto;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.io.FileOutputStream;
import java.io.IOException;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 票务Excel报表服务测试
 */
@SpringBootTest
@ActiveProfiles("test")
class TicketExcelReportServiceTest {

    @Autowired
    private TicketExcelReportService ticketExcelReportService;

    @Autowired
    private TicketStatisticsService ticketStatisticsService;

    @Test
    void testGenerateTicketStatisticsReport() throws IOException {
        // 生成Excel报表
        byte[] excelData = ticketExcelReportService.generateTicketStatisticsReport();
        
        // 验证生成的数据不为空
        assertNotNull(excelData);
        assertTrue(excelData.length > 0);
        
        // 保存到文件进行手动验证（可选）
        try (FileOutputStream fos = new FileOutputStream("target/ticket_report_test.xlsx")) {
            fos.write(excelData);
            System.out.println("测试Excel文件已保存到: target/ticket_report_test.xlsx");
        }
    }

    @Test
    void testGetRegionStatistics() {
        List<TicketStatisticsDto.RegionStatistics> stats = ticketStatisticsService.getRegionStatistics();
        assertNotNull(stats);
        System.out.println("区域统计数据条数: " + stats.size());
        
        for (TicketStatisticsDto.RegionStatistics stat : stats) {
            System.out.println(String.format("区域: %s, 网络票: %d, 团体票: %d, 总计: %d",
                    stat.getRegion(), stat.getNetworkTickets(), stat.getGroupTickets(), stat.getTotalTickets()));
        }
    }

    @Test
    void testGetCityStatistics() {
        List<TicketStatisticsDto.CityStatistics> stats = ticketStatisticsService.getCityStatistics();
        assertNotNull(stats);
        System.out.println("城市统计数据条数: " + stats.size());
        
        for (TicketStatisticsDto.CityStatistics stat : stats) {
            System.out.println(String.format("省份: %s, 城市: %s, 票数: %d",
                    stat.getProvince(), stat.getCity(), stat.getTicketCount()));
        }
    }

    @Test
    void testGetAgeStatistics() {
        List<TicketStatisticsDto.AgeStatistics> stats = ticketStatisticsService.getAgeStatistics();
        assertNotNull(stats);
        System.out.println("年龄统计数据条数: " + stats.size());
        
        for (TicketStatisticsDto.AgeStatistics stat : stats) {
            System.out.println(String.format("年龄组: %s, 票数: %d",
                    stat.getAgeGroup(), stat.getTicketCount()));
        }
    }
}
