import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * 简单的comments字段解析测试
 */
public class TestCommentsParsing {
    
    public static void main(String[] args) {
        ObjectMapper objectMapper = new ObjectMapper();
        
        // 测试用例1: errorMsg有值，comments也有值 - 应该使用errorMsg
        String json1 = "{\"errorMsg\":\"Payment failed\",\"comments\":\"支付失败详情\"}";
        testParsing(objectMapper, json1, "测试用例1: errorMsg有值，comments也有值");
        
        // 测试用例2: errorMsg为空，comments有值 - 应该使用comments
        String json2 = "{\"errorMsg\":\"\",\"comments\":\"用户认证失败，token已过期\"}";
        testParsing(objectMapper, json2, "测试用例2: errorMsg为空，comments有值");
        
        // 测试用例3: errorMsg为null，comments有值 - 应该使用comments
        String json3 = "{\"comments\":\"库存不足，当前库存为0\"}";
        testParsing(objectMapper, json3, "测试用例3: errorMsg为null，comments有值");
        
        // 测试用例4: errorMsg和comments都为空 - 应该为空
        String json4 = "{\"errorMsg\":\"\",\"comments\":\"\"}";
        testParsing(objectMapper, json4, "测试用例4: errorMsg和comments都为空");
        
        // 测试用例5: 都没有这两个字段 - 应该为null
        String json5 = "{\"path\":\"/api/test\",\"status\":1}";
        testParsing(objectMapper, json5, "测试用例5: 都没有这两个字段");
    }
    
    private static void testParsing(ObjectMapper objectMapper, String json, String testCase) {
        try {
            JsonNode jsonNode = objectMapper.readTree(json);
            
            // 模拟LogParseService中的逻辑
            String errorMsg = getStringValue(jsonNode, "errorMsg");
            if (errorMsg == null || errorMsg.trim().isEmpty()) {
                errorMsg = getStringValue(jsonNode, "comments");
            }
            
            System.out.println(testCase);
            System.out.println("输入JSON: " + json);
            System.out.println("解析结果: " + (errorMsg == null ? "null" : "\"" + errorMsg + "\""));
            System.out.println("---");
            
        } catch (Exception e) {
            System.err.println("解析失败: " + e.getMessage());
        }
    }
    
    private static String getStringValue(JsonNode node, String fieldName) {
        JsonNode fieldNode = node.get(fieldName);
        return fieldNode != null && !fieldNode.isNull() ? fieldNode.asText() : null;
    }
}

/*
预期输出:

测试用例1: errorMsg有值，comments也有值
输入JSON: {"errorMsg":"Payment failed","comments":"支付失败详情"}
解析结果: "Payment failed"
---
测试用例2: errorMsg为空，comments有值
输入JSON: {"errorMsg":"","comments":"用户认证失败，token已过期"}
解析结果: "用户认证失败，token已过期"
---
测试用例3: errorMsg为null，comments有值
输入JSON: {"comments":"库存不足，当前库存为0"}
解析结果: "库存不足，当前库存为0"
---
测试用例4: errorMsg和comments都为空
输入JSON: {"errorMsg":"","comments":""}
解析结果: ""
---
测试用例5: 都没有这两个字段
输入JSON: {"path":"/api/test","status":1}
解析结果: null
---
*/
