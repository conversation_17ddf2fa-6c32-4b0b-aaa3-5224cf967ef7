package com.marry.suchaologanalysis.mapper;

import com.marry.suchaologanalysis.dto.TicketStatisticsDto;
import com.marry.suchaologanalysis.entity.TicketPerson;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 票务人员信息Mapper接口
 */
@Mapper
public interface TicketPersonMapper {

    /**
     * 查询所有票务人员信息
     */
    List<TicketPerson> findAll();

    /**
     * 根据ID查询票务人员信息
     */
    TicketPerson findById(@Param("id") Long id);

    /**
     * 查询需要更新省市信息的人员（省份或城市为空的记录）
     */
    List<TicketPerson> findPersonsNeedUpdate();

    /**
     * 查询需要更新年龄信息的人员（年龄为空的记录）
     */
    List<TicketPerson> findPersonsNeedAgeUpdate();

    /**
     * 查询身份证号码不为空但省市或年龄为空的人员
     */
    List<TicketPerson> findPersonsWithIdCardButMissingInfo();

    /**
     * 更新人员的省市信息
     */
    int updateProvinceAndCity(@Param("id") Long id,
                             @Param("province") String province,
                             @Param("city") String city);

    /**
     * 更新人员的年龄信息
     */
    int updateAge(@Param("id") Long id, @Param("age") Integer age);

    /**
     * 更新人员的省市和年龄信息
     */
    int updateProvinceAndCityAndAge(@Param("id") Long id,
                                   @Param("province") String province,
                                   @Param("city") String city,
                                   @Param("age") Integer age);

    /**
     * 更新人员的省市、年龄和性别信息
     */
    int updateProvinceAndCityAndAgeAndGender(@Param("id") Long id,
                                            @Param("province") String province,
                                            @Param("city") String city,
                                            @Param("age") Integer age,
                                            @Param("gender") String gender);

    /**
     * 批量更新人员信息
     */
    int batchUpdatePersonInfo(@Param("persons") List<TicketPerson> persons);

    /**
     * 插入票务人员信息
     */
    int insert(TicketPerson person);

    /**
     * 批量插入票务人员信息
     */
    int batchInsert(@Param("persons") List<TicketPerson> persons);

    /**
     * 更新票务人员信息
     */
    int update(TicketPerson person);

    /**
     * 删除票务人员信息
     */
    int deleteById(@Param("id") Long id);

    /**
     * 统计总记录数
     */
    Long countTotal();

    /**
     * 统计需要更新信息的记录数
     */
    Long countNeedUpdate();

    /**
     * 根据身份证号码查询人员信息
     */
    List<TicketPerson> findByIdCard(@Param("psptId") String psptId);

    /**
     * 获取区域统计数据
     * stock_name表示区域，type为8表示团体票，其他均是网络票
     */
    List<TicketStatisticsDto.RegionStatistics> getRegionStatistics();

    /**
     * 获取城市统计数据
     * 如果城市没有值的话就取省份的值
     */
    List<TicketStatisticsDto.CityStatistics> getCityStatistics();

    /**
     * 获取年龄统计数据
     * 按年龄组进行统计
     */
    List<TicketStatisticsDto.AgeStatistics> getAgeStatistics();

    /**
     * 获取详细年龄统计数据
     * 按具体年龄进行统计（不分组）
     */
    List<TicketStatisticsDto.DetailedAgeStatistics> getDetailedAgeStatistics();
}
