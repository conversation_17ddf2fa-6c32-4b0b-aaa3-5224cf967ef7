package com.marry.suchaologanalysis.entity;

import lombok.Data;

/**
 * 票务人员信息实体类
 */
@Data
public class TicketPerson {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 票务ID
     */
    private Long ticketId;
    
    /**
     * 票务名称
     */
    private String ticketName;
    
    /**
     * 姓名
     */
    private String name;
    
    /**
     * 证件类型
     */
    private String psptType;
    
    /**
     * 证件号码
     */
    private String psptId;
    
    /**
     * 股票名称
     */
    private String stockName;
    
    /**
     * 行号
     */
    private Integer row;
    
    /**
     * 座位号
     */
    private Integer seat;
    
    /**
     * 类型
     */
    private String type;
    
    /**
     * 状态
     */
    private String state;
    
    /**
     * 年龄
     */
    private Integer age;
    
    /**
     * 省份
     */
    private String province;
    
    /**
     * 城市
     */
    private String city;
}
