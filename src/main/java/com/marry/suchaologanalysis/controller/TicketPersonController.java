package com.marry.suchaologanalysis.controller;

import com.marry.suchaologanalysis.entity.TicketPerson;
import com.marry.suchaologanalysis.mapper.TicketPersonMapper;
import com.marry.suchaologanalysis.service.IdCardUpdateService;
import com.marry.suchaologanalysis.util.IdCardUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 票务人员信息控制器
 */
@RestController
@RequestMapping("/ticket")
@Slf4j
public class TicketPersonController {

    @Autowired
    private TicketPersonMapper ticketPersonMapper;

    @Autowired
    private IdCardUpdateService idCardUpdateService;

    /**
     * 获取所有票务人员信息
     */
    @GetMapping("/persons")
    public ResponseEntity<List<TicketPerson>> getAllPersons() {
        try {
            List<TicketPerson> persons = ticketPersonMapper.findAll();
            return ResponseEntity.ok(persons);
        } catch (Exception e) {
            log.error("获取票务人员信息失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 根据ID获取票务人员信息
     */
    @GetMapping("/persons/{id}")
    public ResponseEntity<TicketPerson> getPersonById(@PathVariable Long id) {
        try {
            TicketPerson person = ticketPersonMapper.findById(id);
            if (person != null) {
                return ResponseEntity.ok(person);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            log.error("获取票务人员信息失败, ID: {}", id, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 获取需要更新信息的人员列表
     */
    @GetMapping("/persons/need-update")
    public ResponseEntity<List<TicketPerson>> getPersonsNeedUpdate() {
        try {
            List<TicketPerson> persons = ticketPersonMapper.findPersonsWithIdCardButMissingInfo();
            return ResponseEntity.ok(persons);
        } catch (Exception e) {
            log.error("获取需要更新的人员信息失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 批量更新所有人员的身份证信息
     */
    @PostMapping("/persons/update-all")
    public ResponseEntity<Map<String, Object>> updateAllPersonInfo() {
        try {
            log.info("开始批量更新人员身份证信息...");
            IdCardUpdateService.UpdateResult result = idCardUpdateService.updateAllPersonInfo();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "批量更新完成");
            response.put("totalCount", result.getTotalCount());
            response.put("successCount", result.getSuccessCount());
            response.put("failedCount", result.getFailedCount());
            response.put("skippedCount", result.getSkippedCount());
            response.put("errorMessages", result.getErrorMessages());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("批量更新人员信息失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "批量更新失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 更新指定人员的身份证信息
     */
    @PostMapping("/persons/{id}/update")
    public ResponseEntity<Map<String, Object>> updatePersonInfo(@PathVariable Long id) {
        try {
            boolean success = idCardUpdateService.updatePersonInfoById(id);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", success);
            response.put("message", success ? "更新成功" : "更新失败或无需更新");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("更新人员信息失败, ID: {}", id, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "更新失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 获取更新统计信息
     */
    @GetMapping("/persons/statistics")
    public ResponseEntity<IdCardUpdateService.UpdateStatistics> getUpdateStatistics() {
        try {
            IdCardUpdateService.UpdateStatistics stats = idCardUpdateService.getUpdateStatistics();
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            log.error("获取更新统计信息失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 验证身份证号码
     */
    @PostMapping("/validate-idcard")
    public ResponseEntity<IdCardUtil.IdCardInfo> validateIdCard(@RequestBody Map<String, String> request) {
        try {
            String idCard = request.get("idCard");
            if (idCard == null || idCard.trim().isEmpty()) {
                IdCardUtil.IdCardInfo info = new IdCardUtil.IdCardInfo();
                info.setValid(false);
                info.setErrorMessage("身份证号码不能为空");
                return ResponseEntity.badRequest().body(info);
            }
            
            IdCardUtil.IdCardInfo info = idCardUpdateService.validateIdCard(idCard);
            return ResponseEntity.ok(info);
        } catch (Exception e) {
            log.error("验证身份证号码失败", e);
            IdCardUtil.IdCardInfo info = new IdCardUtil.IdCardInfo();
            info.setValid(false);
            info.setErrorMessage("验证失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(info);
        }
    }

    /**
     * 根据身份证号码查询人员信息
     */
    @GetMapping("/persons/by-idcard/{idCard}")
    public ResponseEntity<List<TicketPerson>> getPersonsByIdCard(@PathVariable String idCard) {
        try {
            List<TicketPerson> persons = ticketPersonMapper.findByIdCard(idCard);
            return ResponseEntity.ok(persons);
        } catch (Exception e) {
            log.error("根据身份证号码查询人员信息失败, 身份证号: {}", idCard, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 创建票务人员信息
     */
    @PostMapping("/persons")
    public ResponseEntity<Map<String, Object>> createPerson(@RequestBody TicketPerson person) {
        try {
            int result = ticketPersonMapper.insert(person);
            
            Map<String, Object> response = new HashMap<>();
            if (result > 0) {
                response.put("success", true);
                response.put("message", "创建成功");
                response.put("id", person.getId());
            } else {
                response.put("success", false);
                response.put("message", "创建失败");
            }
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("创建票务人员信息失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "创建失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 更新票务人员信息
     */
    @PutMapping("/persons/{id}")
    public ResponseEntity<Map<String, Object>> updatePerson(@PathVariable Long id, @RequestBody TicketPerson person) {
        try {
            person.setId(id);
            int result = ticketPersonMapper.update(person);
            
            Map<String, Object> response = new HashMap<>();
            if (result > 0) {
                response.put("success", true);
                response.put("message", "更新成功");
            } else {
                response.put("success", false);
                response.put("message", "更新失败，记录不存在");
            }
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("更新票务人员信息失败, ID: {}", id, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "更新失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 删除票务人员信息
     */
    @DeleteMapping("/persons/{id}")
    public ResponseEntity<Map<String, Object>> deletePerson(@PathVariable Long id) {
        try {
            int result = ticketPersonMapper.deleteById(id);
            
            Map<String, Object> response = new HashMap<>();
            if (result > 0) {
                response.put("success", true);
                response.put("message", "删除成功");
            } else {
                response.put("success", false);
                response.put("message", "删除失败，记录不存在");
            }
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("删除票务人员信息失败, ID: {}", id, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "删除失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
}
