# MyBatis??
mybatis.mapper-locations=classpath:mappers/*.xml
mybatis.type-aliases-package=com.marry.suchaologanalysis.entity

# FreeMarker??
spring.freemarker.template-loader-path=classpath:/templates/
spring.freemarker.suffix=.ftl
spring.freemarker.charset=utf-8
spring.freemarker.cache=false
spring.freemarker.expose-request-attributes=true
spring.freemarker.expose-session-attributes=true

# ??????
spring.resources.static-locations=classpath:/static/

# ??????
server.port=8089

# PostgreSQL?????
spring.datasource.url=**************************************************
spring.datasource.driver-class-name=org.postgresql.Driver
spring.datasource.username=root
spring.datasource.password=123456

# ?????
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.connection-timeout=30000

# ????
logging.level.com.marry.suchaologanalysis=DEBUG
logging.level.org.springframework.batch=INFO
