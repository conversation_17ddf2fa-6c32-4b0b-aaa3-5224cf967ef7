package com.marry.suchaologanalysis.service;

import com.marry.suchaologanalysis.entity.TicketPerson;
import com.marry.suchaologanalysis.mapper.TicketPersonMapper;
import com.marry.suchaologanalysis.util.IdCardUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 身份证信息更新服务
 */
@Service
@Slf4j
public class IdCardUpdateService {

    @Autowired
    private TicketPersonMapper ticketPersonMapper;

    /**
     * 更新结果统计
     */
    public static class UpdateResult {
        private int totalCount;         // 总处理数量
        private int successCount;       // 成功更新数量
        private int failedCount;        // 失败数量
        private int skippedCount;       // 跳过数量
        private List<String> errorMessages; // 错误信息列表

        public UpdateResult() {
            this.errorMessages = new ArrayList<>();
        }

        // Getters and Setters
        public int getTotalCount() { return totalCount; }
        public void setTotalCount(int totalCount) { this.totalCount = totalCount; }
        
        public int getSuccessCount() { return successCount; }
        public void setSuccessCount(int successCount) { this.successCount = successCount; }
        
        public int getFailedCount() { return failedCount; }
        public void setFailedCount(int failedCount) { this.failedCount = failedCount; }
        
        public int getSkippedCount() { return skippedCount; }
        public void setSkippedCount(int skippedCount) { this.skippedCount = skippedCount; }
        
        public List<String> getErrorMessages() { return errorMessages; }
        public void setErrorMessages(List<String> errorMessages) { this.errorMessages = errorMessages; }
        
        public void addErrorMessage(String message) { this.errorMessages.add(message); }
        
        @Override
        public String toString() {
            return String.format("更新结果: 总数=%d, 成功=%d, 失败=%d, 跳过=%d", 
                               totalCount, successCount, failedCount, skippedCount);
        }
    }

    /**
     * 更新所有需要更新的人员信息
     */
    @Transactional
    public UpdateResult updateAllPersonInfo() {
        log.info("开始批量更新人员身份证信息...");
        
        // 查询需要更新的人员
        List<TicketPerson> persons = ticketPersonMapper.findPersonsWithIdCardButMissingInfo();
        
        UpdateResult result = new UpdateResult();
        result.setTotalCount(persons.size());
        
        log.info("找到{}条需要更新的记录", persons.size());
        
        for (TicketPerson person : persons) {
            try {
                boolean updated = updatePersonInfo(person);
                if (updated) {
                    result.setSuccessCount(result.getSuccessCount() + 1);
                } else {
                    result.setSkippedCount(result.getSkippedCount() + 1);
                }
            } catch (Exception e) {
                result.setFailedCount(result.getFailedCount() + 1);
                String errorMsg = String.format("更新人员[ID:%d, 姓名:%s]失败: %s", 
                                               person.getId(), person.getName(), e.getMessage());
                result.addErrorMessage(errorMsg);
                log.error(errorMsg, e);
            }
        }
        
        log.info("批量更新完成: {}", result);
        return result;
    }

    /**
     * 更新单个人员信息
     */
    @Transactional
    public boolean updatePersonInfo(TicketPerson person) {
        if (person == null || person.getPsptId() == null || person.getPsptId().trim().isEmpty()) {
            log.warn("人员信息或身份证号码为空，跳过更新");
            return false;
        }

        // 检查是否为身份证类型
        if (!"400".equals(person.getPsptType())) {
            log.debug("人员[ID:{}]证件类型不是身份证，跳过更新", person.getId());
            return false;
        }

        // 解析身份证信息
        IdCardUtil.IdCardInfo idCardInfo = IdCardUtil.parseIdCard(person.getPsptId());
        
        if (!idCardInfo.isValid()) {
            log.warn("人员[ID:{}, 姓名:{}]身份证号码[{}]解析失败: {}", 
                    person.getId(), person.getName(), person.getPsptId(), idCardInfo.getErrorMessage());
            return false;
        }

        // 检查是否需要更新
        boolean needUpdate = false;
        
        // 检查省份
        if (person.getProvince() == null || person.getProvince().trim().isEmpty()) {
            if (idCardInfo.getProvince() != null && !idCardInfo.getProvince().trim().isEmpty()) {
                person.setProvince(idCardInfo.getProvince());
                needUpdate = true;
            }
        }
        
        // 检查城市
        if (person.getCity() == null || person.getCity().trim().isEmpty()) {
            if (idCardInfo.getCity() != null && !idCardInfo.getCity().trim().isEmpty()) {
                person.setCity(idCardInfo.getCity());
                needUpdate = true;
            }
        }
        
        // 检查年龄
        if (person.getAge() == null || person.getAge() == 0) {
            if (idCardInfo.getAge() != null && idCardInfo.getAge() > 0) {
                person.setAge(idCardInfo.getAge());
                needUpdate = true;
            }
        }

        // 检查性别
        if (person.getGender() == null || person.getGender().trim().isEmpty()) {
            if (idCardInfo.getGender() != null && !idCardInfo.getGender().trim().isEmpty()) {
                person.setGender(idCardInfo.getGender());
                needUpdate = true;
            }
        }

        if (!needUpdate) {
            log.debug("人员[ID:{}]信息已完整，无需更新", person.getId());
            return false;
        }

        // 执行更新
        int updateCount = ticketPersonMapper.updateProvinceAndCityAndAgeAndGender(
            person.getId(), person.getProvince(), person.getCity(), person.getAge(), person.getGender());
        
        if (updateCount > 0) {
            log.info("成功更新人员[ID:{}, 姓名:{}]信息: 省份={}, 城市={}, 年龄={}, 性别={}",
                    person.getId(), person.getName(), person.getProvince(), person.getCity(), person.getAge(), person.getGender());
            return true;
        } else {
            log.warn("更新人员[ID:{}]信息失败，影响行数为0", person.getId());
            return false;
        }
    }

    /**
     * 更新指定人员的信息
     */
    @Transactional
    public boolean updatePersonInfoById(Long personId) {
        TicketPerson person = ticketPersonMapper.findById(personId);
        if (person == null) {
            log.warn("未找到ID为{}的人员信息", personId);
            return false;
        }
        
        return updatePersonInfo(person);
    }

    /**
     * 获取需要更新的人员统计信息
     */
    public UpdateStatistics getUpdateStatistics() {
        Long totalCount = ticketPersonMapper.countTotal();
        Long needUpdateCount = ticketPersonMapper.countNeedUpdate();
        
        UpdateStatistics stats = new UpdateStatistics();
        stats.setTotalPersons(totalCount != null ? totalCount.intValue() : 0);
        stats.setNeedUpdatePersons(needUpdateCount != null ? needUpdateCount.intValue() : 0);
        stats.setCompletedPersons(stats.getTotalPersons() - stats.getNeedUpdatePersons());
        
        return stats;
    }

    /**
     * 更新统计信息
     */
    public static class UpdateStatistics {
        private int totalPersons;          // 总人员数
        private int needUpdatePersons;     // 需要更新的人员数
        private int completedPersons;      // 已完成更新的人员数

        // Getters and Setters
        public int getTotalPersons() { return totalPersons; }
        public void setTotalPersons(int totalPersons) { this.totalPersons = totalPersons; }
        
        public int getNeedUpdatePersons() { return needUpdatePersons; }
        public void setNeedUpdatePersons(int needUpdatePersons) { this.needUpdatePersons = needUpdatePersons; }
        
        public int getCompletedPersons() { return completedPersons; }
        public void setCompletedPersons(int completedPersons) { this.completedPersons = completedPersons; }
        
        public double getCompletionRate() {
            return totalPersons > 0 ? (double) completedPersons / totalPersons * 100 : 0;
        }
        
        @Override
        public String toString() {
            return String.format("统计信息: 总人员=%d, 需更新=%d, 已完成=%d, 完成率=%.2f%%", 
                               totalPersons, needUpdatePersons, completedPersons, getCompletionRate());
        }
    }

    /**
     * 验证身份证号码
     */
    public IdCardUtil.IdCardInfo validateIdCard(String idCard) {
        return IdCardUtil.parseIdCard(idCard);
    }

    /**
     * 批量验证身份证号码
     */
    public List<IdCardUtil.IdCardInfo> batchValidateIdCards(List<String> idCards) {
        List<IdCardUtil.IdCardInfo> results = new ArrayList<>();
        
        for (String idCard : idCards) {
            results.add(IdCardUtil.parseIdCard(idCard));
        }
        
        return results;
    }
}
