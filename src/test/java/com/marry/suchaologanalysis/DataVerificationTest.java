package com.marry.suchaologanalysis;

import com.marry.suchaologanalysis.entity.TicketPerson;
import com.marry.suchaologanalysis.mapper.TicketPersonMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;

/**
 * 数据验证测试
 */
@SpringBootTest
@ActiveProfiles("test")
class DataVerificationTest {

    @Autowired
    private TicketPersonMapper ticketPersonMapper;

    @Test
    void verifyTicketPersonData() {
        System.out.println("=== 验证ticket_person表数据 ===");
        
        try {
            // 查询总记录数
            List<TicketPerson> allTickets = ticketPersonMapper.findAll();
            System.out.println("总记录数: " + allTickets.size());
            
            if (allTickets.isEmpty()) {
                System.out.println("警告：ticket_person表为空！");
                return;
            }
            
            // 显示前5条记录
            System.out.println("\n前5条记录:");
            for (int i = 0; i < Math.min(5, allTickets.size()); i++) {
                TicketPerson ticket = allTickets.get(i);
                System.out.println(String.format("ID: %d, 姓名: %s, 区域: %s, 类型: %s, 省份: %s, 城市: %s, 年龄: %s",
                        ticket.getId(), ticket.getName(), ticket.getStockName(), 
                        ticket.getType(), ticket.getProvince(), ticket.getCity(), ticket.getAge()));
            }
            
            // 统计不同字段的数据情况
            long stockNameCount = allTickets.stream().filter(t -> t.getStockName() != null && !t.getStockName().trim().isEmpty()).count();
            long provinceCount = allTickets.stream().filter(t -> t.getProvince() != null && !t.getProvince().trim().isEmpty()).count();
            long cityCount = allTickets.stream().filter(t -> t.getCity() != null && !t.getCity().trim().isEmpty()).count();
            long ageCount = allTickets.stream().filter(t -> t.getAge() != null).count();
            long type8Count = allTickets.stream().filter(t -> "8".equals(t.getType())).count();
            
            System.out.println("\n数据统计:");
            System.out.println("有stock_name的记录: " + stockNameCount);
            System.out.println("有province的记录: " + provinceCount);
            System.out.println("有city的记录: " + cityCount);
            System.out.println("有age的记录: " + ageCount);
            System.out.println("type=8的记录: " + type8Count);
            System.out.println("其他type的记录: " + (allTickets.size() - type8Count));
            
        } catch (Exception e) {
            System.err.println("数据验证失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
