package com.marry.suchaologanalysis.mapper;

import com.marry.suchaologanalysis.dto.TicketStatisticsDto;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 票务人员Mapper测试
 */
@SpringBootTest
@ActiveProfiles("test")
class TicketPersonMapperTest {

    @Autowired
    private TicketPersonMapper ticketPersonMapper;

    @Test
    void testGetRegionStatistics() {
        try {
            List<TicketStatisticsDto.RegionStatistics> stats = ticketPersonMapper.getRegionStatistics();
            assertNotNull(stats);
            System.out.println("区域统计查询成功，返回 " + stats.size() + " 条记录");
            
            for (TicketStatisticsDto.RegionStatistics stat : stats) {
                System.out.println(String.format("区域: %s, 网络票: %d, 团体票: %d, 总计: %d",
                        stat.getRegion(), stat.getNetworkTickets(), stat.getGroupTickets(), stat.getTotalTickets()));
            }
        } catch (Exception e) {
            System.err.println("区域统计查询失败: " + e.getMessage());
            e.printStackTrace();
            fail("区域统计查询失败: " + e.getMessage());
        }
    }

    @Test
    void testGetCityStatistics() {
        try {
            List<TicketStatisticsDto.CityStatistics> stats = ticketPersonMapper.getCityStatistics();
            assertNotNull(stats);
            System.out.println("城市统计查询成功，返回 " + stats.size() + " 条记录");
            
            for (TicketStatisticsDto.CityStatistics stat : stats) {
                System.out.println(String.format("省份: %s, 城市: %s, 票数: %d",
                        stat.getProvince(), stat.getCity(), stat.getTicketCount()));
            }
        } catch (Exception e) {
            System.err.println("城市统计查询失败: " + e.getMessage());
            e.printStackTrace();
            fail("城市统计查询失败: " + e.getMessage());
        }
    }

    @Test
    void testGetAgeStatistics() {
        try {
            List<TicketStatisticsDto.AgeStatistics> stats = ticketPersonMapper.getAgeStatistics();
            assertNotNull(stats);
            System.out.println("年龄统计查询成功，返回 " + stats.size() + " 条记录");
            
            for (TicketStatisticsDto.AgeStatistics stat : stats) {
                System.out.println(String.format("年龄组: %s, 票数: %d",
                        stat.getAgeGroup(), stat.getTicketCount()));
            }
        } catch (Exception e) {
            System.err.println("年龄统计查询失败: " + e.getMessage());
            e.printStackTrace();
            fail("年龄统计查询失败: " + e.getMessage());
        }
    }
}
