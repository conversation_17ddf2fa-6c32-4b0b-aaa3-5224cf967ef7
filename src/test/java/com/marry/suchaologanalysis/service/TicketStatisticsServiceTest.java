package com.marry.suchaologanalysis.service;

import com.marry.suchaologanalysis.dto.TicketStatisticsDto;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 票务统计服务测试
 */
@SpringBootTest
@ActiveProfiles("test")
class TicketStatisticsServiceTest {

    @Autowired
    private TicketStatisticsService ticketStatisticsService;

    @Test
    void testGetRegionStatistics() {
        try {
            List<TicketStatisticsDto.RegionStatistics> stats = ticketStatisticsService.getRegionStatistics();
            assertNotNull(stats);
            System.out.println("=== 区域统计结果 ===");
            System.out.println("总记录数: " + stats.size());

            for (TicketStatisticsDto.RegionStatistics stat : stats) {
                System.out.println(String.format("区域: %s, 网络票: %d, 团体票: %d, 总计: %d",
                        stat.getRegion(), stat.getNetworkTickets(), stat.getGroupTickets(), stat.getTotalTickets()));
            }

            if (stats.isEmpty()) {
                System.out.println("警告：区域统计返回空结果！");
            }
        } catch (Exception e) {
            System.err.println("区域统计查询失败: " + e.getMessage());
            e.printStackTrace();
            fail("区域统计查询失败: " + e.getMessage());
        }
    }

    @Test
    void testGetCityStatistics() {
        try {
            List<TicketStatisticsDto.CityStatistics> stats = ticketStatisticsService.getCityStatistics();
            assertNotNull(stats);
            System.out.println("=== 城市统计结果 ===");
            System.out.println("总记录数: " + stats.size());

            for (TicketStatisticsDto.CityStatistics stat : stats) {
                System.out.println(String.format("省份: %s, 城市: %s, 票数: %d",
                        stat.getProvince(), stat.getCity(), stat.getTicketCount()));
            }

            if (stats.isEmpty()) {
                System.out.println("警告：城市统计返回空结果！");
            }
        } catch (Exception e) {
            System.err.println("城市统计查询失败: " + e.getMessage());
            e.printStackTrace();
            fail("城市统计查询失败: " + e.getMessage());
        }
    }

    @Test
    void testGetAgeStatistics() {
        try {
            List<TicketStatisticsDto.AgeStatistics> stats = ticketStatisticsService.getAgeStatistics();
            assertNotNull(stats);
            System.out.println("=== 年龄统计结果 ===");
            System.out.println("总记录数: " + stats.size());

            for (TicketStatisticsDto.AgeStatistics stat : stats) {
                System.out.println(String.format("年龄组: %s, 票数: %d",
                        stat.getAgeGroup(), stat.getTicketCount()));
            }

            if (stats.isEmpty()) {
                System.out.println("警告：年龄统计返回空结果！");
            }
        } catch (Exception e) {
            System.err.println("年龄统计查询失败: " + e.getMessage());
            e.printStackTrace();
            fail("年龄统计查询失败: " + e.getMessage());
        }
    }

    @Test
    void testGetDetailedAgeStatistics() {
        try {
            List<TicketStatisticsDto.DetailedAgeStatistics> stats = ticketStatisticsService.getDetailedAgeStatistics();
            assertNotNull(stats);
            System.out.println("=== 详细年龄统计结果 ===");
            System.out.println("总记录数: " + stats.size());

            for (TicketStatisticsDto.DetailedAgeStatistics stat : stats) {
                System.out.println(String.format("年龄: %s, 票数: %d",
                        stat.getAge(), stat.getTicketCount()));
            }

            if (stats.isEmpty()) {
                System.out.println("警告：详细年龄统计返回空结果！");
            }
        } catch (Exception e) {
            System.err.println("详细年龄统计查询失败: " + e.getMessage());
            e.printStackTrace();
            fail("详细年龄统计查询失败: " + e.getMessage());
        }
    }
}
