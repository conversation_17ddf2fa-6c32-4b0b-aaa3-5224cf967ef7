<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>票务统计报表</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .download-section {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 30px;
            text-align: center;
        }

        .download-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }

        .stats-section {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            overflow: hidden;
        }

        .stats-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #dee2e6;
        }

        .stats-header h3 {
            margin: 0;
            color: #495057;
        }

        .stats-content {
            padding: 20px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            align-items: start;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }

        th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #495057;
        }

        tr:hover {
            background-color: #f8f9fa;
        }

        .chart-container {
            position: relative;
            height: 400px;
            width: 100%;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }

        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }

        @media (max-width: 768px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }

            .container {
                padding: 10px;
            }
        }

        /* 标签页样式 */
        .age-tabs {
            width: 100%;
        }

        .tab-buttons {
            display: flex;
            border-bottom: 2px solid #ddd;
            margin-bottom: 20px;
        }

        .tab-button {
            padding: 12px 24px;
            border: none;
            background: #f8f9fa;
            cursor: pointer;
            border-radius: 8px 8px 0 0;
            margin-right: 4px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .tab-button:hover {
            background: #e9ecef;
        }

        .tab-button.active {
            background: #007bff;
            color: white;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面标题 -->
        <div class="header">
            <h1>票务统计报表</h1>
            <p>购票信息统计分析</p>
        </div>

        <!-- 下载区域 -->
        <div class="download-section">
            <h3>Excel报表下载</h3>
            <p>包含区域统计、城市统计、年龄统计三个标签页，每个标签页都包含数据表格和图表说明</p>
            <a href="/ticket-report/download" class="download-btn">
                📊 下载完整Excel报表
            </a>
        </div>

        <!-- 区域统计 -->
        <div class="stats-section">
            <div class="stats-header">
                <h3>区域票务统计</h3>
                <p>按区域统计网络票和团体票的发放数量</p>
            </div>
            <div class="stats-content">
                <div class="stats-grid">
                    <div>
                        <h4>统计数据</h4>
                        <table id="regionTable">
                            <thead>
                                <tr>
                                    <th>区域</th>
                                    <th>网络票</th>
                                    <th>团体票</th>
                                    <th>总计</th>
                                </tr>
                            </thead>
                            <tbody>
                                <#if regionStats?? && regionStats?size gt 0>
                                    <#list regionStats as stat>
                                        <tr>
                                            <td>${stat.region!''}</td>
                                            <td>${stat.networkTickets!0}</td>
                                            <td>${stat.groupTickets!0}</td>
                                            <td>${stat.totalTickets!0}</td>
                                        </tr>
                                    </#list>
                                <#else>
                                    <tr>
                                        <td colspan="4" class="loading">暂无数据</td>
                                    </tr>
                                </#if>
                            </tbody>
                        </table>
                    </div>
                    <div>
                        <h4>分布图表</h4>
                        <div class="chart-container">
                            <canvas id="regionChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 城市统计 -->
        <div class="stats-section">
            <div class="stats-header">
                <h3>城市票务统计</h3>
                <p>按城市统计票数量，如果城市为空则使用省份</p>
            </div>
            <div class="stats-content">
                <div class="stats-grid">
                    <div>
                        <h4>统计数据</h4>
                        <div style="max-height: 400px; overflow-y: auto;">
                            <table id="cityTable">
                                <thead>
                                    <tr>
                                        <th>省份</th>
                                        <th>城市</th>
                                        <th>票数量</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <#if cityStats?? && cityStats?size gt 0>
                                        <#list cityStats as stat>
                                            <tr>
                                                <td>${stat.province!''}</td>
                                                <td>${stat.city!''}</td>
                                                <td>${stat.ticketCount!0}</td>
                                            </tr>
                                        </#list>
                                    <#else>
                                        <tr>
                                            <td colspan="3" class="loading">暂无数据</td>
                                        </tr>
                                    </#if>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div>
                        <h4>分布图表</h4>
                        <div class="chart-container">
                            <canvas id="cityChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 年龄统计 -->
        <div class="stats-section">
            <div class="stats-header">
                <h3>年龄票务统计</h3>
                <p>按年龄统计票数量分布</p>
            </div>
            <div class="stats-content">
                <!-- 年龄统计标签页 -->
                <div class="age-tabs">
                    <div class="tab-buttons">
                        <button class="tab-button active" onclick="showAgeTab('grouped')">年龄分组统计</button>
                        <button class="tab-button" onclick="showAgeTab('detailed')">详细年龄统计</button>
                    </div>

                    <!-- 年龄分组统计 -->
                    <div id="grouped-age-tab" class="tab-content active">
                        <div class="stats-grid">
                            <div>
                                <h4>年龄分组数据</h4>
                                <table id="ageTable">
                                    <thead>
                                        <tr>
                                            <th>年龄组</th>
                                            <th>票数量</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <#if ageStats?? && ageStats?size gt 0>
                                            <#list ageStats as stat>
                                                <tr>
                                                    <td>${stat.ageGroup!''}</td>
                                                    <td>${stat.ticketCount!0}</td>
                                                </tr>
                                            </#list>
                                        <#else>
                                            <tr>
                                                <td colspan="2" class="loading">暂无数据</td>
                                            </tr>
                                        </#if>
                                    </tbody>
                                </table>
                            </div>
                            <div>
                                <h4>年龄分组直方图</h4>
                                <div class="chart-container">
                                    <canvas id="ageChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 详细年龄统计 -->
                    <div id="detailed-age-tab" class="tab-content">
                        <div class="stats-grid">
                            <div>
                                <h4>详细年龄数据</h4>
                                <table id="detailedAgeTable">
                                    <thead>
                                        <tr>
                                            <th>年龄</th>
                                            <th>票数量</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <#if detailedAgeStats?? && detailedAgeStats?size gt 0>
                                            <#list detailedAgeStats as stat>
                                                <tr>
                                                    <td>${stat.age!''}</td>
                                                    <td>${stat.ticketCount!0}</td>
                                                </tr>
                                            </#list>
                                        <#else>
                                            <tr>
                                                <td colspan="2" class="loading">暂无数据</td>
                                            </tr>
                                        </#if>
                                    </tbody>
                                </table>
                            </div>
                            <div>
                                <h4>详细年龄分布图</h4>
                                <div class="chart-container">
                                    <canvas id="detailedAgeChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页面加载完成后初始化图表
        document.addEventListener('DOMContentLoaded', function() {
            initializeCharts();
        });

        function initializeCharts() {
            // 初始化区域图表
            initRegionChart();

            // 初始化城市图表
            initCityChart();

            // 初始化年龄图表
            initAgeChart();

            // 初始化详细年龄图表
            initDetailedAgeChart();
        }

        // 标签页切换函数
        function showAgeTab(tabName) {
            // 隐藏所有标签页内容
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => content.classList.remove('active'));

            // 移除所有按钮的active类
            const tabButtons = document.querySelectorAll('.tab-button');
            tabButtons.forEach(button => button.classList.remove('active'));

            // 显示选中的标签页
            if (tabName === 'grouped') {
                document.getElementById('grouped-age-tab').classList.add('active');
                document.querySelector('[onclick="showAgeTab(\'grouped\')"]').classList.add('active');
            } else if (tabName === 'detailed') {
                document.getElementById('detailed-age-tab').classList.add('active');
                document.querySelector('[onclick="showAgeTab(\'detailed\')"]').classList.add('active');
                // 重新初始化详细年龄图表以确保正确显示
                setTimeout(() => {
                    initDetailedAgeChart();
                }, 100);
            }
        }

        function initRegionChart() {
            const ctx = document.getElementById('regionChart').getContext('2d');

            // 从表格中获取数据
            const regionData = [];
            const networkData = [];
            const groupData = [];

            const table = document.getElementById('regionTable');
            if (!table) {
                console.error('找不到regionTable');
                return;
            }

            const tbody = table.getElementsByTagName('tbody')[0];
            if (!tbody) {
                console.error('找不到regionTable的tbody');
                return;
            }

            const rows = tbody.getElementsByTagName('tr');
            console.log('区域表格行数:', rows.length);

            for (let i = 0; i < rows.length && i < 10; i++) { // 限制显示前10个
                const cells = rows[i].getElementsByTagName('td');
                if (cells.length >= 4) {
                    const regionText = cells[0].textContent.trim();
                    const networkText = cells[1].textContent.trim();
                    const groupText = cells[2].textContent.trim();

                    if (regionText !== '暂无数据' && regionText !== '') {
                        regionData.push(regionText);
                        networkData.push(parseInt(networkText) || 0);
                        groupData.push(parseInt(groupText) || 0);
                        console.log(`区域数据 ${i}: ${regionText}, 网络票: ${networkText}, 团体票: ${groupText}`);
                    }
                }
            }

            console.log('区域图表数据:', { regionData, networkData, groupData });

            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: regionData,
                    datasets: [{
                        label: '网络票',
                        data: networkData,
                        backgroundColor: 'rgba(54, 162, 235, 0.8)',
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 1
                    }, {
                        label: '团体票',
                        data: groupData,
                        backgroundColor: 'rgba(255, 99, 132, 0.8)',
                        borderColor: 'rgba(255, 99, 132, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: '区域票务分布'
                        }
                    }
                }
            });
        }

        function initCityChart() {
            const ctx = document.getElementById('cityChart').getContext('2d');

            // 从表格中获取数据
            const cityData = [];
            const countData = [];

            const table = document.getElementById('cityTable');
            if (!table) {
                console.error('找不到cityTable');
                return;
            }

            const tbody = table.getElementsByTagName('tbody')[0];
            if (!tbody) {
                console.error('找不到cityTable的tbody');
                return;
            }

            const rows = tbody.getElementsByTagName('tr');
            console.log('城市表格行数:', rows.length);

            for (let i = 0; i < rows.length && i < 15; i++) { // 限制显示前15个
                const cells = rows[i].getElementsByTagName('td');
                if (cells.length >= 3) {
                    const provinceText = cells[0].textContent.trim();
                    const cityText = cells[1].textContent.trim();
                    const countText = cells[2].textContent.trim();

                    if (provinceText !== '暂无数据' && provinceText !== '') {
                        const displayCity = cityText || provinceText;
                        cityData.push(displayCity);
                        countData.push(parseInt(countText) || 0);
                        console.log(`城市数据 ${i}: ${displayCity}, 票数: ${countText}`);
                    }
                }
            }

            console.log('城市图表数据:', { cityData, countData });

            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: cityData,
                    datasets: [{
                        label: '票数量',
                        data: countData,
                        backgroundColor: 'rgba(75, 192, 192, 0.8)',
                        borderColor: 'rgba(75, 192, 192, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        },
                        x: {
                            ticks: {
                                maxRotation: 45
                            }
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: '城市票务分布'
                        }
                    }
                }
            });
        }

        function initAgeChart() {
            const ctx = document.getElementById('ageChart').getContext('2d');

            // 从表格中获取数据
            const ageData = [];
            const countData = [];

            const table = document.getElementById('ageTable');
            if (!table) {
                console.error('找不到ageTable');
                return;
            }

            const tbody = table.getElementsByTagName('tbody')[0];
            if (!tbody) {
                console.error('找不到ageTable的tbody');
                return;
            }

            const rows = tbody.getElementsByTagName('tr');
            console.log('年龄表格行数:', rows.length);

            for (let i = 0; i < rows.length; i++) {
                const cells = rows[i].getElementsByTagName('td');
                if (cells.length >= 2) {
                    const ageGroupText = cells[0].textContent.trim();
                    const countText = cells[1].textContent.trim();

                    if (ageGroupText !== '暂无数据' && ageGroupText !== '') {
                        ageData.push(ageGroupText);
                        countData.push(parseInt(countText) || 0);
                        console.log(`年龄数据 ${i}: ${ageGroupText}, 票数: ${countText}`);
                    }
                }
            }

            console.log('年龄图表数据:', { ageData, countData });

            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ageData,
                    datasets: [{
                        label: '票数量',
                        data: countData,
                        backgroundColor: 'rgba(153, 102, 255, 0.8)',
                        borderColor: 'rgba(153, 102, 255, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: '年龄分布直方图'
                        }
                    }
                }
            });
        }

        function initDetailedAgeChart() {
            const ctx = document.getElementById('detailedAgeChart').getContext('2d');

            // 从表格中获取数据
            const ageData = [];
            const countData = [];

            const table = document.getElementById('detailedAgeTable');
            if (!table) {
                console.error('找不到detailedAgeTable');
                return;
            }

            const tbody = table.getElementsByTagName('tbody')[0];
            if (!tbody) {
                console.error('找不到detailedAgeTable的tbody');
                return;
            }

            const rows = tbody.getElementsByTagName('tr');
            console.log('详细年龄表格行数:', rows.length);

            for (let i = 0; i < rows.length && i < 50; i++) { // 限制显示前50个年龄
                const cells = rows[i].getElementsByTagName('td');
                if (cells.length >= 2) {
                    const ageText = cells[0].textContent.trim();
                    const countText = cells[1].textContent.trim();

                    if (ageText !== '暂无数据' && ageText !== '') {
                        ageData.push(ageText);
                        countData.push(parseInt(countText) || 0);
                        console.log(`详细年龄数据 ${i}: ${ageText}, 票数: ${countText}`);
                    }
                }
            }

            console.log('详细年龄图表数据:', { ageData, countData });

            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ageData,
                    datasets: [{
                        label: '票数量',
                        data: countData,
                        backgroundColor: 'rgba(255, 159, 64, 0.8)',
                        borderColor: 'rgba(255, 159, 64, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        },
                        x: {
                            ticks: {
                                maxRotation: 90,
                                minRotation: 45
                            }
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: '详细年龄分布图'
                        }
                    }
                }
            });
        }
    </script>
</body>
</html>
