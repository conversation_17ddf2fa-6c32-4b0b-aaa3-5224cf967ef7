[2025-07-01 15:02:23.276] {"path":"/api/project/skill/performTicketOrder","ajax":true,"status":1,"errorCode":"","errorMsg":"","time":156,"startTime":"2025-07-01 15:02:23.120","endTime":"2025-07-01 15:02:23.276","className":"com.marry.suchao.controller.ProjectController","method":"performTicketOrder","param":{"ip":"*************","netUserId":12345,"orderId":"ORD001","amount":99.99}}
[2025-07-01 15:03:15.432] {"path":"/api/user/login","ajax":true,"status":1,"errorCode":"","errorMsg":"","time":89,"startTime":"2025-07-01 15:03:15.343","endTime":"2025-07-01 15:03:15.432","className":"com.marry.suchao.controller.UserController","method":"login","param":{"ip":"*************","netUserId":12346,"username":"testuser","loginType":"password"}}
[2025-07-01 15:04:22.567] {"path":"/api/product/search","ajax":false,"status":1,"errorCode":"","errorMsg":"","time":234,"startTime":"2025-07-01 15:04:22.333","endTime":"2025-07-01 15:04:22.567","className":"com.marry.suchao.controller.ProductController","method":"search","param":{"ip":"*************","netUserId":12347,"keyword":"laptop","category":"electronics"}}
[2025-07-01 15:05:33.789] {"path":"/api/order/create","ajax":true,"status":0,"errorCode":"PAYMENT_FAILED","errorMsg":"Payment gateway timeout","time":5000,"startTime":"2025-07-01 15:05:28.789","endTime":"2025-07-01 15:05:33.789","className":"com.marry.suchao.controller.OrderController","method":"createOrder","param":{"ip":"*************","netUserId":12348,"productId":"PROD123","quantity":2}}
[2025-07-01 15:06:44.123] {"path":"/api/user/profile","ajax":true,"status":1,"errorCode":"","errorMsg":"","time":67,"startTime":"2025-07-01 15:06:44.056","endTime":"2025-07-01 15:06:44.123","className":"com.marry.suchao.controller.UserController","method":"getProfile","param":{"ip":"*************","netUserId":12349,"profileType":"basic"}}
[2025-07-01 15:07:55.456] {"path":"/api/cart/add","ajax":true,"status":1,"errorCode":"","errorMsg":"","time":123,"startTime":"2025-07-01 15:07:55.333","endTime":"2025-07-01 15:07:55.456","className":"com.marry.suchao.controller.CartController","method":"addToCart","param":{"ip":"*************","netUserId":12350,"productId":"PROD456","quantity":1}}
[2025-07-01 15:08:11.789] {"path":"/api/payment/process","ajax":true,"status":0,"errorCode":"INSUFFICIENT_FUNDS","errorMsg":"Insufficient account balance","time":890,"startTime":"2025-07-01 15:08:10.899","endTime":"2025-07-01 15:08:11.789","className":"com.marry.suchao.controller.PaymentController","method":"processPayment","param":{"ip":"*************","netUserId":12351,"amount":299.99,"paymentMethod":"credit_card"}}
[2025-07-01 15:09:22.345] {"path":"/api/inventory/check","ajax":false,"status":1,"errorCode":"","errorMsg":"","time":45,"startTime":"2025-07-01 15:09:22.300","endTime":"2025-07-01 15:09:22.345","className":"com.marry.suchao.controller.InventoryController","method":"checkStock","param":{"ip":"*************","netUserId":12352,"productId":"PROD789","warehouseId":"WH001"}}
[2025-07-01 15:10:33.678] {"path":"/api/notification/send","ajax":true,"status":1,"errorCode":"","errorMsg":"","time":178,"startTime":"2025-07-01 15:10:33.500","endTime":"2025-07-01 15:10:33.678","className":"com.marry.suchao.controller.NotificationController","method":"sendNotification","param":{"ip":"*************","netUserId":12353,"type":"email","message":"Order confirmation"}}
[2025-07-01 15:11:44.901] {"path":"/api/analytics/track","ajax":true,"status":1,"errorCode":"","errorMsg":"","time":34,"startTime":"2025-07-01 15:11:44.867","endTime":"2025-07-01 15:11:44.901","className":"com.marry.suchao.controller.AnalyticsController","method":"trackEvent","param":{"ip":"*************","netUserId":12354,"event":"page_view","page":"/products"}}
[2025-07-01 16:02:23.276] {"path":"/api/project/skill/performTicketOrder","ajax":true,"status":1,"errorCode":"","errorMsg":"","time":145,"startTime":"2025-07-01 16:02:23.131","endTime":"2025-07-01 16:02:23.276","className":"com.marry.suchao.controller.ProjectController","method":"performTicketOrder","param":{"ip":"*************","netUserId":12355,"orderId":"ORD002","amount":149.99}}
[2025-07-01 16:03:15.432] {"path":"/api/user/logout","ajax":true,"status":1,"errorCode":"","errorMsg":"","time":23,"startTime":"2025-07-01 16:03:15.409","endTime":"2025-07-01 16:03:15.432","className":"com.marry.suchao.controller.UserController","method":"logout","param":{"ip":"*************","netUserId":12356,"sessionId":"SES123"}}
[2025-07-01 16:04:22.567] {"path":"/api/product/details","ajax":false,"status":1,"errorCode":"","errorMsg":"","time":189,"startTime":"2025-07-01 16:04:22.378","endTime":"2025-07-01 16:04:22.567","className":"com.marry.suchao.controller.ProductController","method":"getDetails","param":{"ip":"*************","netUserId":12357,"productId":"PROD999","includeReviews":true}}
[2025-07-01 16:05:33.789] {"path":"/api/order/status","ajax":true,"status":1,"errorCode":"","errorMsg":"","time":78,"startTime":"2025-07-01 16:05:33.711","endTime":"2025-07-01 16:05:33.789","className":"com.marry.suchao.controller.OrderController","method":"getOrderStatus","param":{"ip":"*************","netUserId":12358,"orderId":"ORD003"}}
[2025-07-01 16:06:44.123] {"path":"/api/user/settings","ajax":true,"status":0,"errorCode":"VALIDATION_ERROR","errorMsg":"Invalid email format","time":156,"startTime":"2025-07-01 16:06:43.967","endTime":"2025-07-01 16:06:44.123","className":"com.marry.suchao.controller.UserController","method":"updateSettings","param":{"ip":"*************","netUserId":12359,"email":"invalid-email","notifications":true}}
[2025-07-01 17:02:23.276] {"path":"/api/report/generate","ajax":false,"status":1,"errorCode":"","errorMsg":"","time":2345,"startTime":"2025-07-01 17:02:20.931","endTime":"2025-07-01 17:02:23.276","className":"com.marry.suchao.controller.ReportController","method":"generateReport","param":{"ip":"*************","netUserId":12360,"reportType":"sales","dateRange":"2025-07-01"}}
[2025-07-01 17:03:15.432] {"path":"/api/backup/create","ajax":true,"status":1,"errorCode":"","errorMsg":"","time":12345,"startTime":"2025-07-01 17:03:03.087","endTime":"2025-07-01 17:03:15.432","className":"com.marry.suchao.controller.BackupController","method":"createBackup","param":{"ip":"*************","netUserId":12361,"backupType":"full","compression":true}}
[2025-07-01 17:04:22.567] {"path":"/api/cache/clear","ajax":true,"status":1,"errorCode":"","errorMsg":"","time":567,"startTime":"2025-07-01 17:04:22.000","endTime":"2025-07-01 17:04:22.567","className":"com.marry.suchao.controller.CacheController","method":"clearCache","param":{"ip":"*************","netUserId":12362,"cacheType":"all","force":true}}
[2025-07-01 17:05:33.789] {"path":"/api/health/check","ajax":false,"status":1,"errorCode":"","errorMsg":"","time":12,"startTime":"2025-07-01 17:05:33.777","endTime":"2025-07-01 17:05:33.789","className":"com.marry.suchao.controller.HealthController","method":"healthCheck","param":{"ip":"*************","netUserId":null,"checkType":"basic"}}
[2025-07-01 17:06:44.123] {"path":"/api/metrics/collect","ajax":true,"status":1,"errorCode":"","errorMsg":"","time":89,"startTime":"2025-07-01 17:06:44.034","endTime":"2025-07-01 17:06:44.123","className":"com.marry.suchao.controller.MetricsController","method":"collectMetrics","param":{"ip":"*************","netUserId":12363,"metricType":"performance","interval":"1h"}}
