package com.marry.suchaologanalysis.service;

import com.marry.suchaologanalysis.dto.TicketStatisticsDto;
import com.marry.suchaologanalysis.mapper.TicketPersonMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 票务统计服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TicketStatisticsService {

    private final TicketPersonMapper ticketPersonMapper;

    /**
     * 获取区域统计数据
     * stock_name表示区域，type为8表示团体票，其他均是网络票
     */
    public List<TicketStatisticsDto.RegionStatistics> getRegionStatistics() {
        log.info("开始获取区域统计数据");
        try {
            List<TicketStatisticsDto.RegionStatistics> result = 
                ticketPersonMapper.getRegionStatistics();
            log.info("获取区域统计数据成功，共{}条记录", result.size());
            return result;
        } catch (Exception e) {
            log.error("获取区域统计数据失败", e);
            throw new RuntimeException("获取区域统计数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取城市统计数据
     * 如果城市没有值的话就取省份的值
     */
    public List<TicketStatisticsDto.CityStatistics> getCityStatistics() {
        log.info("开始获取城市统计数据");
        try {
            List<TicketStatisticsDto.CityStatistics> result = 
                ticketPersonMapper.getCityStatistics();
            log.info("获取城市统计数据成功，共{}条记录", result.size());
            return result;
        } catch (Exception e) {
            log.error("获取城市统计数据失败", e);
            throw new RuntimeException("获取城市统计数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取年龄统计数据
     * 按年龄组进行统计
     */
    public List<TicketStatisticsDto.AgeStatistics> getAgeStatistics() {
        log.info("开始获取年龄统计数据");
        try {
            List<TicketStatisticsDto.AgeStatistics> result = 
                ticketPersonMapper.getAgeStatistics();
            log.info("获取年龄统计数据成功，共{}条记录", result.size());
            return result;
        } catch (Exception e) {
            log.error("获取年龄统计数据失败", e);
            throw new RuntimeException("获取年龄统计数据失败: " + e.getMessage(), e);
        }
    }
}
