package com.marry.suchaologanalysis.controller;

import com.marry.suchaologanalysis.dto.TicketStatisticsDto;
import com.marry.suchaologanalysis.service.TicketExcelReportService;
import com.marry.suchaologanalysis.service.TicketStatisticsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 票务报表控制器
 */
@Slf4j
@Controller
@RequestMapping("/ticket-report")
@RequiredArgsConstructor
public class TicketReportController {

    private final TicketStatisticsService ticketStatisticsService;
    private final TicketExcelReportService ticketExcelReportService;

    /**
     * 显示票务统计页面
     */
    @GetMapping
    public String showTicketReport(Model model) {
        try {
            // 获取统计数据
            List<TicketStatisticsDto.RegionStatistics> regionStats = 
                ticketStatisticsService.getRegionStatistics();
            List<TicketStatisticsDto.CityStatistics> cityStats = 
                ticketStatisticsService.getCityStatistics();
            List<TicketStatisticsDto.AgeStatistics> ageStats = 
                ticketStatisticsService.getAgeStatistics();

            model.addAttribute("regionStats", regionStats);
            model.addAttribute("cityStats", cityStats);
            model.addAttribute("ageStats", ageStats);

            return "ticket-report";
        } catch (Exception e) {
            log.error("获取票务统计数据失败", e);
            model.addAttribute("error", "获取统计数据失败: " + e.getMessage());
            return "error";
        }
    }

    /**
     * 下载Excel报表
     */
    @GetMapping("/download")
    @ResponseBody
    public ResponseEntity<byte[]> downloadExcelReport() {
        try {
            log.info("开始生成Excel报表");
            
            // 生成Excel报表
            byte[] excelData = ticketExcelReportService.generateTicketStatisticsReport();
            
            // 生成文件名
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String fileName = "票务统计报表_" + timestamp + ".xlsx";
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);
            
            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", encodedFileName);
            headers.setContentLength(excelData.length);
            
            log.info("Excel报表生成成功，文件大小: {} bytes", excelData.length);
            
            return new ResponseEntity<>(excelData, headers, HttpStatus.OK);
            
        } catch (IOException e) {
            log.error("生成Excel报表失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(("生成Excel报表失败: " + e.getMessage()).getBytes(StandardCharsets.UTF_8));
        } catch (Exception e) {
            log.error("下载Excel报表时发生未知错误", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(("下载报表失败: " + e.getMessage()).getBytes(StandardCharsets.UTF_8));
        }
    }

    /**
     * 获取区域统计数据API
     */
    @GetMapping("/api/region-stats")
    @ResponseBody
    public ResponseEntity<List<TicketStatisticsDto.RegionStatistics>> getRegionStatistics() {
        try {
            List<TicketStatisticsDto.RegionStatistics> stats = 
                ticketStatisticsService.getRegionStatistics();
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            log.error("获取区域统计数据失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 获取城市统计数据API
     */
    @GetMapping("/api/city-stats")
    @ResponseBody
    public ResponseEntity<List<TicketStatisticsDto.CityStatistics>> getCityStatistics() {
        try {
            List<TicketStatisticsDto.CityStatistics> stats = 
                ticketStatisticsService.getCityStatistics();
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            log.error("获取城市统计数据失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 获取年龄统计数据API
     */
    @GetMapping("/api/age-stats")
    @ResponseBody
    public ResponseEntity<List<TicketStatisticsDto.AgeStatistics>> getAgeStatistics() {
        try {
            List<TicketStatisticsDto.AgeStatistics> stats = 
                ticketStatisticsService.getAgeStatistics();
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            log.error("获取年龄统计数据失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}
