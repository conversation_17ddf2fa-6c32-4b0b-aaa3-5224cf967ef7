package com.marry.suchaologanalysis.service;

import com.marry.suchaologanalysis.entity.LogRecord;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

public class LogParseServiceTest {

    private LogParseService logParseService;

    @BeforeEach
    void setUp() {
        logParseService = new LogParseService();
    }

    @Test
    void testParseLogLine() {
        String logLine = "[2025-07-01 15:02:23.276] {\"path\":\"/api/project/skill/performTicketOrder\",\"ajax\":true,\"status\":1,\"errorCode\":\"\",\"errorMsg\":\"\",\"time\":156,\"startTime\":\"2025-07-01 15:02:23.120\",\"endTime\":\"2025-07-01 15:02:23.276\",\"className\":\"com.marry.suchao.controller.ProjectController\",\"method\":\"performTicketOrder\",\"param\":{\"ip\":\"*************\",\"netUserId\":12345,\"other\":\"data\"}}";
        
        LogRecord record = logParseService.parseLogLine(logLine, "test-server");
        
        assertNotNull(record);
        assertEquals("/api/project/skill/performTicketOrder", record.getPath());
        assertTrue(record.getAjax());
        assertEquals(1, record.getStatus());
        assertEquals(156L, record.getTime());
        assertEquals("com.marry.suchao.controller.ProjectController", record.getClassName());
        assertEquals("performTicketOrder", record.getMethod());
        assertEquals("*************", record.getIp());
        assertEquals(12345L, record.getNetUserId());
        assertEquals("test-server", record.getMachineName());
        
        // 验证时间戳解析
        LocalDateTime expectedTimestamp = LocalDateTime.of(2025, 7, 1, 15, 2, 23, 276000000);
        assertEquals(expectedTimestamp, record.getTimestamp());
    }

    @Test
    void testParseLogLineWithInvalidFormat() {
        String invalidLogLine = "Invalid log format";
        
        LogRecord record = logParseService.parseLogLine(invalidLogLine, "test-server");
        
        assertNull(record);
    }

    @Test
    void testParseLogLineWithMissingTimestamp() {
        String logLineWithoutTimestamp = "{\"path\":\"/api/test\",\"status\":1}";
        
        LogRecord record = logParseService.parseLogLine(logLineWithoutTimestamp, "test-server");
        
        assertNull(record);
    }

    @Test
    void testParseLogLineWithInvalidJson() {
        String logLineWithInvalidJson = "[2025-07-01 15:02:23.276] {\"path\":\"/api/test\",\"status\":1,}";
        
        LogRecord record = logParseService.parseLogLine(logLineWithInvalidJson, "test-server");
        
        assertNull(record);
    }

    @Test
    void testParseLogLineWithMinimalData() {
        String minimalLogLine = "[2025-07-01 15:02:23.276] {\"path\":\"/api/test\"}";
        
        LogRecord record = logParseService.parseLogLine(minimalLogLine, "test-server");
        
        assertNotNull(record);
        assertEquals("/api/test", record.getPath());
        assertEquals("test-server", record.getMachineName());
        assertNull(record.getAjax());
        assertNull(record.getStatus());
    }
}
