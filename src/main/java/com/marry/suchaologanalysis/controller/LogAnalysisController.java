package com.marry.suchaologanalysis.controller;

import com.marry.suchaologanalysis.entity.LogStatistics;
import com.marry.suchaologanalysis.service.LogImportService;
import com.marry.suchaologanalysis.service.LogStatisticsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 日志分析控制器
 */
@Controller
@RequestMapping("/log")
@Slf4j
public class LogAnalysisController {
    
    @Autowired
    private LogImportService logImportService;
    
    @Autowired
    private LogStatisticsService logStatisticsService;
    
    /**
     * 首页
     */
    @GetMapping("/")
    public String index(Model model) {
        // 获取机器名称列表
        List<String> machineNames = logStatisticsService.getAllMachineNames();
        model.addAttribute("machineNames", machineNames);

        // 获取路径列表
        List<String> paths = logStatisticsService.getAllPaths();
        model.addAttribute("paths", paths);

        // 获取日志总数
        Long totalCount = logImportService.getTotalLogCount();
        model.addAttribute("totalCount", totalCount);

        return "index";
    }
    
    /**
     * 导入页面
     */
    @GetMapping("/import")
    public String importPage() {
        return "import";
    }
    
    /**
     * 上传并导入日志文件
     */
    @PostMapping("/upload")
    @ResponseBody
    public Map<String, Object> uploadLogFile(@RequestParam("file") MultipartFile file,
                                           @RequestParam("machineName") String machineName) {
        Map<String, Object> result = new HashMap<>();
        
        if (file.isEmpty()) {
            result.put("success", false);
            result.put("message", "文件不能为空");
            return result;
        }
        
        if (machineName == null || machineName.trim().isEmpty()) {
            result.put("success", false);
            result.put("message", "机器名称不能为空");
            return result;
        }
        
        try {
            // 保存上传的文件
            String fileName = file.getOriginalFilename();
            File tempFile = File.createTempFile("log_", "_" + fileName);
            file.transferTo(tempFile);
            
            // 导入日志
            logImportService.importLogFile(tempFile.getAbsolutePath(), machineName.trim());
            
            // 删除临时文件
            tempFile.delete();
            
            result.put("success", true);
            result.put("message", "文件导入成功");
            
        } catch (IOException e) {
            log.error("文件导入失败", e);
            result.put("success", false);
            result.put("message", "文件导入失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 批量导入本地日志文件
     */
    @PostMapping("/import-local")
    @ResponseBody
    public Map<String, Object> importLocalFiles(@RequestBody List<LogImportService.LogFileInfo> fileInfos) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            logImportService.importLogFilesWithMachines(fileInfos);
            result.put("success", true);
            result.put("message", "批量导入成功");
        } catch (Exception e) {
            log.error("批量导入失败", e);
            result.put("success", false);
            result.put("message", "批量导入失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 获取按小时统计数据
     */
    @GetMapping("/statistics/hourly")
    @ResponseBody
    public List<LogStatistics> getHourlyStatistics(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime,
            @RequestParam(required = false) String machineName) {
        
        if (machineName != null && !machineName.trim().isEmpty()) {
            return logStatisticsService.getHourlyStatisticsByMachine(startTime, endTime, machineName);
        } else {
            return logStatisticsService.getHourlyStatistics(startTime, endTime);
        }
    }
    
    /**
     * 获取按天统计数据
     */
    @GetMapping("/statistics/daily")
    @ResponseBody
    public List<LogStatistics> getDailyStatistics(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        
        return logStatisticsService.getDailyStatistics(startTime, endTime);
    }
    
    /**
     * 获取错误统计数据
     */
    @GetMapping("/statistics/errors")
    @ResponseBody
    public List<LogStatistics> getErrorStatistics(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        
        return logStatisticsService.getErrorStatistics(startTime, endTime);
    }
    
    /**
     * 获取响应时间统计数据
     */
    @GetMapping("/statistics/response-time")
    @ResponseBody
    public List<LogStatistics> getResponseTimeStatistics(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {

        return logStatisticsService.getResponseTimeStatistics(startTime, endTime);
    }

    /**
     * 获取按路径统计数据
     */
    @GetMapping("/statistics/path")
    @ResponseBody
    public List<LogStatistics> getPathStatistics(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime,
            @RequestParam(required = false) String machineName) {

        if (machineName != null && !machineName.trim().isEmpty()) {
            return logStatisticsService.getPathStatisticsByMachine(startTime, endTime, machineName);
        } else {
            return logStatisticsService.getPathStatistics(startTime, endTime);
        }
    }

    /**
     * 清空日志表
     */
    @PostMapping("/clear")
    @ResponseBody
    public Map<String, Object> clearLogs() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            logImportService.clearLogTable();
            result.put("success", true);
            result.put("message", "日志表已清空");
        } catch (Exception e) {
            log.error("清空日志表失败", e);
            result.put("success", false);
            result.put("message", "清空失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 按方法统计（按小时分组）
     */
    @GetMapping("/statistics/method/hourly")
    @ResponseBody
    public List<LogStatistics> getMethodStatisticsByHour(
            @RequestParam String startTime,
            @RequestParam String endTime,
            @RequestParam(required = false) String machineName) {

        LocalDateTime start = LocalDateTime.parse(startTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        LocalDateTime end = LocalDateTime.parse(endTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        if (machineName != null && !machineName.trim().isEmpty()) {
            return logStatisticsService.getMethodStatisticsByHourAndMachine(start, end, machineName.trim());
        } else {
            return logStatisticsService.getMethodStatisticsByHour(start, end);
        }
    }

    /**
     * 按方法统计（按天分组）
     */
    @GetMapping("/statistics/method/daily")
    @ResponseBody
    public List<LogStatistics> getMethodStatisticsByDay(
            @RequestParam String startTime,
            @RequestParam String endTime,
            @RequestParam(required = false) String machineName) {

        LocalDateTime start = LocalDateTime.parse(startTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        LocalDateTime end = LocalDateTime.parse(endTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        if (machineName != null && !machineName.trim().isEmpty()) {
            return logStatisticsService.getMethodStatisticsByDayAndMachine(start, end, machineName.trim());
        } else {
            return logStatisticsService.getMethodStatisticsByDay(start, end);
        }
    }
}
