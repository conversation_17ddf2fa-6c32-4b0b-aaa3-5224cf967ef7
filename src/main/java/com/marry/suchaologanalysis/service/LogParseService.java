package com.marry.suchaologanalysis.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.marry.suchaologanalysis.entity.LogRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 日志解析服务
 */
@Service
@Slf4j
public class LogParseService {

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");

    // 日志行的正则表达式
    private final Pattern logPattern = Pattern.compile("\\[(\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}\\.\\d{3})\\] (.+)");

    /**
     * 解析日志文件
     */
    public List<LogRecord> parseLogFile(String filePath) throws IOException {
        return parseLogFile(filePath, null);
    }

    /**
     * 解析日志文件（指定机器名称）
     */
    public List<LogRecord> parseLogFile(String filePath, String machineName) throws IOException {
        List<LogRecord> records = new ArrayList<>();

        try (BufferedReader reader = new BufferedReader(new FileReader(filePath))) {
            String line;
            int lineNumber = 0;

            while ((line = reader.readLine()) != null) {
                lineNumber++;
                try {
                    LogRecord record = parseLogLine(line, machineName);
                    if (record != null) {
                        records.add(record);
                    }
                } catch (Exception e) {
                    log.warn("解析第{}行日志失败: {}, 错误: {}", lineNumber, line, e.getMessage());
                }
            }
        }

        log.info("成功解析日志文件: {}, 共解析{}条记录", filePath, records.size());
        return records;
    }

    /**
     * 解析单行日志
     */
    public LogRecord parseLogLine(String line) {
        return parseLogLine(line, null);
    }

    /**
     * 解析单行日志（指定机器名称）
     */
    public LogRecord parseLogLine(String line, String machineName) {
        if (line == null || line.trim().isEmpty()) {
            return null;
        }

        Matcher matcher = logPattern.matcher(line);
        if (!matcher.matches()) {
            return null;
        }

        String timestampStr = matcher.group(1);
        String jsonStr = matcher.group(2);

        try {
            LocalDateTime timestamp = LocalDateTime.parse(timestampStr, formatter);
            JsonNode jsonNode = objectMapper.readTree(jsonStr);

            LogRecord record = new LogRecord();
            record.setTimestamp(timestamp);
            record.setPath(getStringValue(jsonNode, "path"));
            record.setAjax(getBooleanValue(jsonNode, "ajax"));
            record.setStatus(getIntegerValue(jsonNode, "status"));
            record.setErrorCode(getStringValue(jsonNode, "errorCode"));
            record.setErrorMsg(getStringValue(jsonNode, "errorMsg"));
            record.setTime(getLongValue(jsonNode, "time"));

            // 解析开始和结束时间
            String startStr = getStringValue(jsonNode, "start");
            String endStr = getStringValue(jsonNode, "end");
            if (startStr != null) {
                record.setStartTime(LocalDateTime.parse(startStr, formatter));
            }
            if (endStr != null) {
                record.setEndTime(LocalDateTime.parse(endStr, formatter));
            }

            record.setClassName(getStringValue(jsonNode, "className"));
            record.setMethod(getStringValue(jsonNode, "method"));

            // 解析param对象 - 存储完整JSON和提取常用字段
            JsonNode paramNode = jsonNode.get("param");
            if (paramNode != null) {
                record.setParam(paramNode.toString()); // 存储完整JSON
                // 提取常用字段
                record.setIp(getStringValue(paramNode, "ip"));
                record.setNetUserId(getLongValue(paramNode, "netUserId"));
            }

            // 设置机器名称
            record.setMachineName(machineName);

            return record;

        } catch (Exception e) {
            log.warn("解析JSON失败: {}, 错误: {}", jsonStr, e.getMessage());
            return null;
        }
    }

    private String getStringValue(JsonNode node, String fieldName) {
        JsonNode field = node.get(fieldName);
        return field != null && !field.isNull() ? field.asText() : null;
    }

    private Integer getIntegerValue(JsonNode node, String fieldName) {
        JsonNode field = node.get(fieldName);
        return field != null && !field.isNull() ? field.asInt() : null;
    }

    private Long getLongValue(JsonNode node, String fieldName) {
        JsonNode field = node.get(fieldName);
        return field != null && !field.isNull() ? field.asLong() : null;
    }

    private Boolean getBooleanValue(JsonNode node, String fieldName) {
        JsonNode field = node.get(fieldName);
        return field != null && !field.isNull() ? field.asBoolean() : null;
    }
}
