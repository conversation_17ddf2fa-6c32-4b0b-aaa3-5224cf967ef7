<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>JavaScript变量修复测试</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <h1>JavaScript变量引用修复测试</h1>

    <canvas id="hourlyChart" width="400" height="200"></canvas>
    <canvas id="responseTimeChart" width="400" height="200"></canvas>
    <canvas id="errorChart" width="400" height="200"></canvas>

    <script>
        // 模拟修复后的变量使用方式
        console.log('测试JavaScript变量修复...');

        // 1. loadHourlyChart 函数变量测试
        function testHourlyChart() {
            console.log('测试 hourlyChart 变量:');

            // 模拟数据
            const hourlyData = [
                { timeLabel: '2025-07-01 10:00:00', totalCount: 100, successCount: 95, errorCount: 5 },
                { timeLabel: '2025-07-01 11:00:00', totalCount: 120, successCount: 115, errorCount: 5 }
            ];

            // 获取canvas上下文
            const hourlyCtx = document.getElementById('hourlyChart').getContext('2d');

            // 创建图表 - 使用正确的变量名
            const hourlyChart = new Chart(hourlyCtx, {
                type: 'line',
                data: {
                    labels: hourlyData.map(item => item.timeLabel),
                    datasets: [{
                        label: '总请求数',
                        data: hourlyData.map(item => item.totalCount),
                        borderColor: 'rgb(75, 192, 192)'
                    }]
                }
            });

            console.log('✓ hourlyChart 变量正确');
        }

        // 2. loadResponseTimeChart 函数变量测试
        function testResponseTimeChart() {
            console.log('测试 responseTimeChart 变量:');

            // 模拟数据
            const responseTimeData = [
                { timeLabel: '<100ms', totalCount: 50 },
                { timeLabel: '100-500ms', totalCount: 30 },
                { timeLabel: '>500ms', totalCount: 20 }
            ];

            // 获取canvas上下文
            const responseTimeCtx = document.getElementById('responseTimeChart').getContext('2d');

            // 创建图表 - 使用正确的变量名
            const responseTimeChart = new Chart(responseTimeCtx, {
                type: 'doughnut',
                data: {
                    labels: responseTimeData.map(item => item.timeLabel),
                    datasets: [{
                        data: responseTimeData.map(item => item.totalCount),
                        backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56']
                    }]
                }
            });

            console.log('✓ responseTimeChart 变量正确');
        }

        // 3. loadErrorChart 函数变量测试
        function testErrorChart() {
            console.log('测试 errorChart 变量:');

            // 模拟数据
            const errorData = [
                { timeLabel: '404', totalCount: 15 },
                { timeLabel: '500', totalCount: 8 },
                { timeLabel: '403', totalCount: 3 }
            ];

            // 获取canvas上下文
            const errorCtx = document.getElementById('errorChart').getContext('2d');

            // 创建图表 - 使用正确的变量名
            const errorChart = new Chart(errorCtx, {
                type: 'bar',
                data: {
                    labels: errorData.map(item => item.errorMessage || '未知错误'),
                    datasets: [{
                        label: '错误次数',
                        data: errorData.map(item => item.totalCount),
                        backgroundColor: 'rgba(255, 99, 132, 0.2)',
                        borderColor: 'rgba(255, 99, 132, 1)'
                    }]
                }
            });

            console.log('✓ errorChart 变量正确');
        }

        // 运行测试
        window.onload = function() {
            try {
                testHourlyChart();
                testResponseTimeChart();
                testErrorChart();
                console.log('🎉 所有JavaScript变量修复测试通过！');
            } catch (error) {
                console.error('❌ 测试失败:', error);
            }
        };

        // 修复总结：
        // 1. hourlyChart: 使用 hourlyCtx, hourlyData 变量
        // 2. responseTimeChart: 使用 responseTimeCtx, responseTimeData 变量
        // 3. errorChart: 使用 errorCtx, errorData 变量
        // 4. 避免了变量名冲突和未定义错误
    </script>
</body>
</html>
