<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.marry.suchaologanalysis.mapper.TicketPersonMapper">

    <!-- 结果映射 -->
    <resultMap id="TicketPersonResultMap" type="com.marry.suchaologanalysis.entity.TicketPerson">
        <id column="id" property="id"/>
        <result column="ticket_id" property="ticketId"/>
        <result column="ticket_name" property="ticketName"/>
        <result column="name" property="name"/>
        <result column="pspt_type" property="psptType"/>
        <result column="pspt_id" property="psptId"/>
        <result column="stock_name" property="stockName"/>
        <result column="row" property="row"/>
        <result column="seat" property="seat"/>
        <result column="type" property="type"/>
        <result column="state" property="state"/>
        <result column="age" property="age"/>
        <result column="province" property="province"/>
        <result column="city" property="city"/>
        <result column="gender" property="gender"/>
    </resultMap>

    <!-- 查询所有票务人员信息 -->
    <select id="findAll" resultMap="TicketPersonResultMap">
        SELECT * FROM ticket_person ORDER BY id
    </select>

    <!-- 根据ID查询票务人员信息 -->
    <select id="findById" resultMap="TicketPersonResultMap">
        SELECT * FROM ticket_person WHERE id = #{id}
    </select>

    <!-- 查询需要更新省市信息的人员 -->
    <select id="findPersonsNeedUpdate" resultMap="TicketPersonResultMap">
        SELECT * FROM ticket_person
        WHERE pspt_id IS NOT NULL
        AND pspt_id != ''
        AND pspt_type = '400'
        AND (province IS NULL OR province = '' OR city IS NULL OR city = '')
        ORDER BY id
    </select>

    <!-- 查询需要更新年龄信息的人员 -->
    <select id="findPersonsNeedAgeUpdate" resultMap="TicketPersonResultMap">
        SELECT * FROM ticket_person
        WHERE pspt_id IS NOT NULL
        AND pspt_id != ''
        AND pspt_type = '400'
        AND (age IS NULL OR age = 0)
        ORDER BY id
    </select>

    <!-- 查询身份证号码不为空但省市或年龄为空的人员 -->
    <select id="findPersonsWithIdCardButMissingInfo" resultMap="TicketPersonResultMap">
        SELECT * FROM ticket_person
        WHERE pspt_id IS NOT NULL
        AND pspt_id != ''
        AND pspt_type = '400'
        AND (
            province IS NULL OR province = ''
            OR city IS NULL OR city = ''
            OR age IS NULL OR age = 0
            OR gender IS NULL OR gender = ''
        )
        ORDER BY id
    </select>

    <!-- 更新人员的省市信息 -->
    <update id="updateProvinceAndCity">
        UPDATE ticket_person
        SET province = #{province}, city = #{city}
        WHERE id = #{id}
    </update>

    <!-- 更新人员的年龄信息 -->
    <update id="updateAge">
        UPDATE ticket_person
        SET age = #{age}
        WHERE id = #{id}
    </update>

    <!-- 更新人员的省市和年龄信息 -->
    <update id="updateProvinceAndCityAndAge">
        UPDATE ticket_person
        SET province = #{province}, city = #{city}, age = #{age}
        WHERE id = #{id}
    </update>

    <!-- 更新人员的省市、年龄和性别信息 -->
    <update id="updateProvinceAndCityAndAgeAndGender">
        UPDATE ticket_person
        SET province = #{province}, city = #{city}, age = #{age}, gender = #{gender}
        WHERE id = #{id}
    </update>

    <!-- 批量更新人员信息 -->
    <update id="batchUpdatePersonInfo">
        <foreach collection="persons" item="person" separator=";">
            UPDATE ticket_person
            SET
            <trim suffixOverrides=",">
                <if test="person.province != null">province = #{person.province},</if>
                <if test="person.city != null">city = #{person.city},</if>
                <if test="person.age != null">age = #{person.age},</if>
                <if test="person.gender != null">gender = #{person.gender},</if>
            </trim>
            WHERE id = #{person.id}
        </foreach>
    </update>

    <!-- 插入票务人员信息 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO ticket_person (
            ticket_id, ticket_name, name, pspt_type, pspt_id,
            stock_name, row, seat, type, state, age, province, city, gender
        ) VALUES (
            #{ticketId}, #{ticketName}, #{name}, #{psptType}, #{psptId},
            #{stockName}, #{row}, #{seat}, #{type}, #{state}, #{age}, #{province}, #{city}, #{gender}
        )
    </insert>

    <!-- 批量插入票务人员信息 -->
    <insert id="batchInsert">
        INSERT INTO ticket_person (
            ticket_id, ticket_name, name, pspt_type, pspt_id,
            stock_name, row, seat, type, state, age, province, city, gender
        ) VALUES
        <foreach collection="persons" item="person" separator=",">
            (
                #{person.ticketId}, #{person.ticketName}, #{person.name},
                #{person.psptType}, #{person.psptId}, #{person.stockName},
                #{person.row}, #{person.seat}, #{person.type}, #{person.state},
                #{person.age}, #{person.province}, #{person.city}, #{person.gender}
            )
        </foreach>
    </insert>

    <!-- 更新票务人员信息 -->
    <update id="update">
        UPDATE ticket_person
        SET
            ticket_id = #{ticketId},
            ticket_name = #{ticketName},
            name = #{name},
            pspt_type = #{psptType},
            pspt_id = #{psptId},
            stock_name = #{stockName},
            row = #{row},
            seat = #{seat},
            type = #{type},
            state = #{state},
            age = #{age},
            province = #{province},
            city = #{city},
            gender = #{gender}
        WHERE id = #{id}
    </update>

    <!-- 删除票务人员信息 -->
    <delete id="deleteById">
        DELETE FROM ticket_person WHERE id = #{id}
    </delete>

    <!-- 统计总记录数 -->
    <select id="countTotal" resultType="java.lang.Long">
        SELECT COUNT(*) FROM ticket_person
    </select>

    <!-- 统计需要更新信息的记录数 -->
    <select id="countNeedUpdate" resultType="java.lang.Long">
        SELECT COUNT(*) FROM ticket_person
        WHERE pspt_id IS NOT NULL
        AND pspt_id != ''
        AND pspt_type = '400'
        AND (
            province IS NULL OR province = ''
            OR city IS NULL OR city = ''
            OR age IS NULL OR age = 0
            OR gender IS NULL OR gender = ''
        )
    </select>

    <!-- 根据身份证号码查询人员信息 -->
    <select id="findByIdCard" resultMap="TicketPersonResultMap">
        SELECT * FROM ticket_person
        WHERE pspt_id = #{psptId}
        ORDER BY id
    </select>

    <!-- 获取区域统计数据 -->
    <select id="getRegionStatistics" resultType="com.marry.suchaologanalysis.dto.TicketStatisticsDto$RegionStatistics">
        SELECT
            stock_name as region,
            SUM(CASE WHEN type != '8' THEN 1 ELSE 0 END) as networkTickets,
            SUM(CASE WHEN type = '8' THEN 1 ELSE 0 END) as groupTickets,
            COUNT(*) as totalTickets
        FROM ticket_person
        WHERE stock_name IS NOT NULL AND stock_name != ''
        GROUP BY stock_name
        ORDER BY totalTickets DESC
    </select>

    <!-- 获取城市统计数据 -->
    <select id="getCityStatistics" resultType="com.marry.suchaologanalysis.dto.TicketStatisticsDto$CityStatistics">
        SELECT
            province,
            CASE
                WHEN city IS NOT NULL AND city != '' THEN city
                ELSE province
            END as city,
            COUNT(*) as ticketCount
        FROM ticket_person
        WHERE (province IS NOT NULL AND province != '')
           OR (city IS NOT NULL AND city != '')
        GROUP BY province,
                 CASE
                     WHEN city IS NOT NULL AND city != '' THEN city
                     ELSE province
                 END
        ORDER BY ticketCount DESC
    </select>

    <!-- 获取年龄统计数据 -->
    <select id="getAgeStatistics" resultType="com.marry.suchaologanalysis.dto.TicketStatisticsDto$AgeStatistics">
        SELECT
            CASE
                WHEN age IS NULL THEN '未知'
                WHEN age &lt; 18 THEN '未成年(0-17岁)'
                WHEN age BETWEEN 18 AND 25 THEN '青年(18-25岁)'
                WHEN age BETWEEN 26 AND 35 THEN '青年(26-35岁)'
                WHEN age BETWEEN 36 AND 45 THEN '中年(36-45岁)'
                WHEN age BETWEEN 46 AND 55 THEN '中年(46-55岁)'
                WHEN age BETWEEN 56 AND 65 THEN '中老年(56-65岁)'
                WHEN age > 65 THEN '老年(65岁以上)'
                ELSE '其他'
            END as ageGroup,
            COUNT(*) as ticketCount
        FROM ticket_person
        GROUP BY
            CASE
                WHEN age IS NULL THEN '未知'
                WHEN age &lt; 18 THEN '未成年(0-17岁)'
                WHEN age BETWEEN 18 AND 25 THEN '青年(18-25岁)'
                WHEN age BETWEEN 26 AND 35 THEN '青年(26-35岁)'
                WHEN age BETWEEN 36 AND 45 THEN '中年(36-45岁)'
                WHEN age BETWEEN 46 AND 55 THEN '中年(46-55岁)'
                WHEN age BETWEEN 56 AND 65 THEN '中老年(56-65岁)'
                WHEN age > 65 THEN '老年(65岁以上)'
                ELSE '其他'
            END
        ORDER BY
            CASE
                WHEN age IS NULL THEN 999
                WHEN age &lt; 18 THEN 1
                WHEN age BETWEEN 18 AND 25 THEN 2
                WHEN age BETWEEN 26 AND 35 THEN 3
                WHEN age BETWEEN 36 AND 45 THEN 4
                WHEN age BETWEEN 46 AND 55 THEN 5
                WHEN age BETWEEN 56 AND 65 THEN 6
                WHEN age > 65 THEN 7
                ELSE 8
            END
    </select>

</mapper>
