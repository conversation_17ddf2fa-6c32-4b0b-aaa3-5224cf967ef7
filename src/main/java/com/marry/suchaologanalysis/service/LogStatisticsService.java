package com.marry.suchaologanalysis.service;

import com.marry.suchaologanalysis.entity.LogStatistics;
import com.marry.suchaologanalysis.mapper.LogRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 日志统计服务
 */
@Service
@Slf4j
public class LogStatisticsService {
    
    @Autowired
    private LogRecordMapper logRecordMapper;
    
    /**
     * 获取按小时统计的数据
     */
    public List<LogStatistics> getHourlyStatistics(LocalDateTime startTime, LocalDateTime endTime) {
        return logRecordMapper.getHourlyStatistics(startTime, endTime);
    }
    
    /**
     * 获取按小时统计的数据（指定机器）
     */
    public List<LogStatistics> getHourlyStatisticsByMachine(LocalDateTime startTime, LocalDateTime endTime, String machineName) {
        return logRecordMapper.getHourlyStatisticsByMachine(startTime, endTime, machineName);
    }
    
    /**
     * 获取按天统计的数据
     */
    public List<LogStatistics> getDailyStatistics(LocalDateTime startTime, LocalDateTime endTime) {
        return logRecordMapper.getDailyStatistics(startTime, endTime);
    }
    
    /**
     * 获取错误统计
     */
    public List<LogStatistics> getErrorStatistics(LocalDateTime startTime, LocalDateTime endTime) {
        return logRecordMapper.getErrorStatistics(startTime, endTime);
    }
    
    /**
     * 获取响应时间统计
     */
    public List<LogStatistics> getResponseTimeStatistics(LocalDateTime startTime, LocalDateTime endTime) {
        return logRecordMapper.getResponseTimeStatistics(startTime, endTime);
    }

    /**
     * 获取按路径统计的数据
     */
    public List<LogStatistics> getPathStatistics(LocalDateTime startTime, LocalDateTime endTime) {
        return logRecordMapper.getPathStatistics(startTime, endTime);
    }

    /**
     * 获取按路径统计的数据（指定机器）
     */
    public List<LogStatistics> getPathStatisticsByMachine(LocalDateTime startTime, LocalDateTime endTime, String machineName) {
        return logRecordMapper.getPathStatisticsByMachine(startTime, endTime, machineName);
    }

    /**
     * 获取所有路径列表
     */
    public List<String> getAllPaths() {
        return logRecordMapper.getAllPaths();
    }

    /**
     * 获取所有机器名称
     */
    public List<String> getAllMachineNames() {
        return logRecordMapper.getAllMachineNames();
    }

    /**
     * 按方法统计（按小时分组）
     */
    public List<LogStatistics> getMethodStatisticsByHour(LocalDateTime startTime, LocalDateTime endTime) {
        return logRecordMapper.getMethodStatisticsByHour(startTime, endTime);
    }

    /**
     * 按方法统计（按小时分组，指定机器）
     */
    public List<LogStatistics> getMethodStatisticsByHourAndMachine(LocalDateTime startTime, LocalDateTime endTime, String machineName) {
        return logRecordMapper.getMethodStatisticsByHourAndMachine(startTime, endTime, machineName);
    }

    /**
     * 按方法统计（按天分组）
     */
    public List<LogStatistics> getMethodStatisticsByDay(LocalDateTime startTime, LocalDateTime endTime) {
        return logRecordMapper.getMethodStatisticsByDay(startTime, endTime);
    }

    /**
     * 按方法统计（按天分组，指定机器）
     */
    public List<LogStatistics> getMethodStatisticsByDayAndMachine(LocalDateTime startTime, LocalDateTime endTime, String machineName) {
        return logRecordMapper.getMethodStatisticsByDayAndMachine(startTime, endTime, machineName);
    }

    /**
     * 获取所有方法列表
     */
    public List<String> getAllMethods() {
        return logRecordMapper.getAllMethods();
    }
}
