-- 创建日志记录表
CREATE TABLE IF NOT EXISTS log_records (
    id BIGSERIAL PRIMARY KEY,
    timestamp TIMESTAMP NOT NULL,
    path VARCHAR(500),
    ajax BOOLEAN,
    status INTEGER,
    error_code VARCHAR(50),
    error_msg TEXT,
    time BIGINT,
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    class_name VARCHAR(500),
    method VARCHAR(200),
    param JSONB,
    ip VARCHAR(50),
    net_user_id BIGINT,
    machine_name VARCHAR(100) NOT NULL,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_log_records_timestamp ON log_records(timestamp);
CREATE INDEX IF NOT EXISTS idx_log_records_machine_name ON log_records(machine_name);
CREATE INDEX IF NOT EXISTS idx_log_records_status ON log_records(status);
CREATE INDEX IF NOT EXISTS idx_log_records_path ON log_records(path);
CREATE INDEX IF NOT EXISTS idx_log_records_machine_timestamp ON log_records(machine_name, timestamp);

-- 为JSONB字段创建GIN索引以支持JSON查询
CREATE INDEX IF NOT EXISTS idx_log_records_param_gin ON log_records USING GIN(param);

-- 添加注释
COMMENT ON TABLE log_records IS '日志记录表';
COMMENT ON COLUMN log_records.id IS '主键ID';
COMMENT ON COLUMN log_records.timestamp IS '日志时间戳';
COMMENT ON COLUMN log_records.path IS '请求路径';
COMMENT ON COLUMN log_records.ajax IS '是否为Ajax请求';
COMMENT ON COLUMN log_records.status IS '状态码';
COMMENT ON COLUMN log_records.error_code IS '错误代码';
COMMENT ON COLUMN log_records.error_msg IS '错误消息';
COMMENT ON COLUMN log_records.time IS '响应时间(毫秒)';
COMMENT ON COLUMN log_records.start_time IS '开始时间';
COMMENT ON COLUMN log_records.end_time IS '结束时间';
COMMENT ON COLUMN log_records.class_name IS '类名';
COMMENT ON COLUMN log_records.method IS '方法名';
COMMENT ON COLUMN log_records.param IS '参数JSON';
COMMENT ON COLUMN log_records.ip IS 'IP地址';
COMMENT ON COLUMN log_records.net_user_id IS '网络用户ID';
COMMENT ON COLUMN log_records.machine_name IS '机器名称';
COMMENT ON COLUMN log_records.create_time IS '创建时间';
