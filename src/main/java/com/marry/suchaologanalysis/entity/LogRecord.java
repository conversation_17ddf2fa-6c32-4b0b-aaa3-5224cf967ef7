package com.marry.suchaologanalysis.entity;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 日志记录实体类
 */
@Data
public class LogRecord {
    private Long id;
    private LocalDateTime timestamp;
    private String path;
    private Boolean ajax;
    private Integer status;
    private String errorCode;
    private String errorMsg;
    private Long time;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private String className;
    private String method;
    private String param; // 存储完整的JSON字符串
    private String ip; // 从param中提取的常用字段
    private Long netUserId; // 从param中提取的常用字段
    private String machineName; // 机器名称，用于区分不同机器的日志
    private LocalDateTime createTime;
}
