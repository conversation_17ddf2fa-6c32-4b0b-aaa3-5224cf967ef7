-- PostgreSQL GROUP BY 测试查询
-- 这些查询应该不会再出现 "column must appear in the GROUP BY clause" 错误

-- 1. 按小时统计（修复后）
SELECT
    TO_CHAR(DATE_TRUNC('hour', timestamp), 'YYYY-MM-DD HH24:00:00') as timeLabel,
    DATE_TRUNC('hour', timestamp) as timePoint,
    COUNT(*) as totalCount,
    SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as successCount,
    SUM(CASE WHEN status != 1 THEN 1 ELSE 0 END) as errorCount,
    AVG(time) as avgResponseTime,
    MAX(time) as maxResponseTime,
    MIN(time) as minResponseTime,
    (SUM(CASE WHEN status != 1 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) as errorRate
FROM log_records
WHERE timestamp BETWEEN '2025-07-01 00:00:00' AND '2025-07-01 23:59:59'
GROUP BY DATE_TRUNC('hour', timestamp)
ORDER BY timePoint;

-- 2. 按天统计（修复后）
SELECT
    TO_CHAR(DATE_TRUNC('day', timestamp), 'YYYY-MM-DD') as timeLabel,
    DATE_TRUNC('day', timestamp) as timePoint,
    COUNT(*) as totalCount,
    SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as successCount,
    SUM(CASE WHEN status != 1 THEN 1 ELSE 0 END) as errorCount,
    AVG(time) as avgResponseTime,
    MAX(time) as maxResponseTime,
    MIN(time) as minResponseTime,
    (SUM(CASE WHEN status != 1 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) as errorRate
FROM log_records
WHERE timestamp BETWEEN '2025-07-01 00:00:00' AND '2025-07-01 23:59:59'
GROUP BY DATE_TRUNC('day', timestamp)
ORDER BY timePoint;

-- 3. 错误统计（修复后）
SELECT
    error_code as timeLabel,
    COUNT(*) as totalCount,
    error_msg as errorMessage
FROM log_records
WHERE timestamp BETWEEN '2025-07-01 00:00:00' AND '2025-07-01 23:59:59'
AND status != 1
GROUP BY error_code, error_msg
ORDER BY totalCount DESC;

-- 4. 响应时间分布统计（正确的）
SELECT
    CASE
        WHEN time < 100 THEN '<100ms'
        WHEN time < 500 THEN '100-500ms'
        WHEN time < 1000 THEN '500ms-1s'
        WHEN time < 3000 THEN '1-3s'
        ELSE '>3s'
    END as timeLabel,
    COUNT(*) as totalCount
FROM log_records
WHERE timestamp BETWEEN '2025-07-01 00:00:00' AND '2025-07-01 23:59:59'
GROUP BY
    CASE
        WHEN time < 100 THEN '<100ms'
        WHEN time < 500 THEN '100-500ms'
        WHEN time < 1000 THEN '500ms-1s'
        WHEN time < 3000 THEN '1-3s'
        ELSE '>3s'
    END
ORDER BY MIN(time);

-- 修复说明：
-- 1. 将 TO_CHAR(timestamp, ...) 改为 TO_CHAR(DATE_TRUNC(..., timestamp), ...)
--    这样确保 SELECT 中的表达式与 GROUP BY 中的表达式一致
-- 2. 修复了错误统计查询中重复的 timeLabel 别名
-- 3. 所有非聚合列都正确地出现在 GROUP BY 子句中
