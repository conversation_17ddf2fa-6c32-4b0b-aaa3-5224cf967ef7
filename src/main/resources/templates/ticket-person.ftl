<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>票务人员信息管理</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .stats-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }

        .stat-card h3 {
            color: #666;
            margin-bottom: 10px;
            font-size: 0.9em;
        }

        .stat-card .value {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
        }

        .controls {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .control-group {
            display: flex;
            flex-direction: column;
            margin-bottom: 15px;
        }

        .control-row {
            display: flex;
            gap: 15px;
            align-items: end;
            flex-wrap: wrap;
        }

        .control-group label {
            font-weight: bold;
            margin-bottom: 5px;
            color: #555;
        }

        .control-group input, .control-group select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            min-width: 200px;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
        }

        .btn-warning {
            background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
        }

        .btn-danger {
            background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
        }

        .table-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 30px;
        }

        .table-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #dee2e6;
        }

        .table-header h3 {
            margin: 0;
            color: #495057;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }

        th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #495057;
        }

        tr:hover {
            background-color: #f8f9fa;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }

        .status-complete {
            background-color: #d4edda;
            color: #155724;
        }

        .status-incomplete {
            background-color: #f8d7da;
            color: #721c24;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }

        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transition: width 0.3s ease;
        }

        .idcard-validator {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .validation-result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }

        .validation-result.valid {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .validation-result.invalid {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .control-row {
                flex-direction: column;
            }
            
            .control-group input, .control-group select {
                min-width: 100%;
            }
            
            table {
                font-size: 12px;
            }
            
            th, td {
                padding: 8px 4px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>票务人员信息管理</h1>
            <p>基于身份证号码自动提取省市信息和年龄</p>
        </div>

        <!-- 统计信息 -->
        <div class="stats-summary">
            <div class="stat-card">
                <h3>总人员数</h3>
                <div class="value" id="totalPersons">-</div>
            </div>
            <div class="stat-card">
                <h3>需要更新</h3>
                <div class="value" id="needUpdatePersons">-</div>
            </div>
            <div class="stat-card">
                <h3>已完成</h3>
                <div class="value" id="completedPersons">-</div>
            </div>
            <div class="stat-card">
                <h3>完成率</h3>
                <div class="value" id="completionRate">-</div>
            </div>
        </div>

        <!-- 身份证验证器 -->
        <div class="idcard-validator">
            <h3>身份证号码验证器</h3>
            <div class="control-row">
                <div class="control-group">
                    <label>身份证号码</label>
                    <input type="text" id="idCardInput" placeholder="请输入18位身份证号码">
                </div>
                <button class="btn" onclick="validateIdCard()">验证</button>
            </div>
            <div id="validationResult" class="validation-result"></div>
        </div>

        <!-- 操作控制 -->
        <div class="controls">
            <h3>批量操作</h3>
            <div class="control-row">
                <button class="btn btn-success" onclick="updateAllPersons()">批量更新所有人员信息</button>
                <button class="btn" onclick="loadPersons()">刷新人员列表</button>
                <button class="btn" onclick="loadNeedUpdatePersons()">查看需要更新的人员</button>
                <button class="btn" onclick="loadStatistics()">刷新统计信息</button>
            </div>
            <div id="updateProgress" style="display: none;">
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill" style="width: 0%"></div>
                </div>
                <div id="progressText">正在处理...</div>
            </div>
        </div>

        <!-- 人员列表 -->
        <div class="table-container">
            <div class="table-header">
                <h3>人员信息列表</h3>
            </div>
            <div id="personsTableContainer">
                <div class="loading">正在加载人员信息...</div>
            </div>
        </div>
    </div>

    <script>
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadStatistics();
            loadPersons();
        });

        // 加载统计信息
        function loadStatistics() {
            fetch('/ticket/persons/statistics')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('totalPersons').textContent = data.totalPersons || 0;
                    document.getElementById('needUpdatePersons').textContent = data.needUpdatePersons || 0;
                    document.getElementById('completedPersons').textContent = data.completedPersons || 0;
                    document.getElementById('completionRate').textContent = (data.completionRate || 0).toFixed(1) + '%';
                })
                .catch(error => {
                    console.error('加载统计信息失败:', error);
                    showError('加载统计信息失败: ' + error.message);
                });
        }

        // 加载人员列表
        function loadPersons() {
            document.getElementById('personsTableContainer').innerHTML = '<div class="loading">正在加载人员信息...</div>';
            
            fetch('/ticket/persons')
                .then(response => response.json())
                .then(data => {
                    renderPersonsTable(data);
                })
                .catch(error => {
                    console.error('加载人员信息失败:', error);
                    document.getElementById('personsTableContainer').innerHTML = '<div class="error">加载人员信息失败: ' + error.message + '</div>';
                });
        }

        // 加载需要更新的人员
        function loadNeedUpdatePersons() {
            document.getElementById('personsTableContainer').innerHTML = '<div class="loading">正在加载需要更新的人员信息...</div>';
            
            fetch('/ticket/persons/need-update')
                .then(response => response.json())
                .then(data => {
                    renderPersonsTable(data, true);
                })
                .catch(error => {
                    console.error('加载需要更新的人员信息失败:', error);
                    document.getElementById('personsTableContainer').innerHTML = '<div class="error">加载需要更新的人员信息失败: ' + error.message + '</div>';
                });
        }

        // 渲染人员表格
        function renderPersonsTable(persons, isNeedUpdateList = false) {
            if (!persons || persons.length === 0) {
                document.getElementById('personsTableContainer').innerHTML = 
                    '<div class="loading">' + (isNeedUpdateList ? '没有需要更新的人员' : '暂无人员信息') + '</div>';
                return;
            }

            let tableHtml = '<table><thead><tr>' +
                '<th>ID</th><th>姓名</th><th>身份证号</th><th>省份</th><th>城市</th><th>年龄</th>' +
                '<th>状态</th><th>操作</th>' +
                '</tr></thead><tbody>';

            persons.forEach(person => {
                const isComplete = person.province && person.city && person.age;
                const statusClass = isComplete ? 'status-complete' : 'status-incomplete';
                const statusText = isComplete ? '信息完整' : '需要更新';

                tableHtml += '<tr>' +
                    '<td>' + (person.id || '') + '</td>' +
                    '<td>' + (person.name || '') + '</td>' +
                    '<td>' + (person.psptId || '') + '</td>' +
                    '<td>' + (person.province || '-') + '</td>' +
                    '<td>' + (person.city || '-') + '</td>' +
                    '<td>' + (person.age || '-') + '</td>' +
                    '<td><span class="status-badge ' + statusClass + '">' + statusText + '</span></td>' +
                    '<td><button class="btn btn-warning" onclick="updateSinglePerson(' + person.id + ')">更新</button></td>' +
                    '</tr>';
            });

            tableHtml += '</tbody></table>';
            document.getElementById('personsTableContainer').innerHTML = tableHtml;
        }

        // 批量更新所有人员信息
        function updateAllPersons() {
            if (!confirm('确定要批量更新所有人员的身份证信息吗？')) {
                return;
            }

            showProgress(true);
            
            fetch('/ticket/persons/update-all', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                showProgress(false);
                
                if (data.success) {
                    showSuccess('批量更新完成！总数: ' + data.totalCount + 
                               ', 成功: ' + data.successCount + 
                               ', 失败: ' + data.failedCount + 
                               ', 跳过: ' + data.skippedCount);
                    
                    if (data.errorMessages && data.errorMessages.length > 0) {
                        console.warn('更新错误信息:', data.errorMessages);
                    }
                    
                    // 刷新数据
                    loadStatistics();
                    loadPersons();
                } else {
                    showError('批量更新失败: ' + data.message);
                }
            })
            .catch(error => {
                showProgress(false);
                console.error('批量更新失败:', error);
                showError('批量更新失败: ' + error.message);
            });
        }

        // 更新单个人员信息
        function updateSinglePerson(personId) {
            fetch('/ticket/persons/' + personId + '/update', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showSuccess('人员信息更新成功！');
                    loadStatistics();
                    loadPersons();
                } else {
                    showError('更新失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('更新人员信息失败:', error);
                showError('更新失败: ' + error.message);
            });
        }

        // 验证身份证号码
        function validateIdCard() {
            const idCard = document.getElementById('idCardInput').value.trim();
            const resultDiv = document.getElementById('validationResult');
            
            if (!idCard) {
                resultDiv.className = 'validation-result invalid';
                resultDiv.innerHTML = '请输入身份证号码';
                resultDiv.style.display = 'block';
                return;
            }

            fetch('/ticket/validate-idcard', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ idCard: idCard })
            })
            .then(response => response.json())
            .then(data => {
                if (data.valid) {
                    resultDiv.className = 'validation-result valid';
                    resultDiv.innerHTML = 
                        '<strong>验证成功！</strong><br>' +
                        '省份: ' + (data.province || '未知') + '<br>' +
                        '城市: ' + (data.city || '未知') + '<br>' +
                        '年龄: ' + (data.age || '未知') + '岁<br>' +
                        '性别: ' + (data.gender || '未知') + '<br>' +
                        '出生日期: ' + (data.birthDate || '未知');
                } else {
                    resultDiv.className = 'validation-result invalid';
                    resultDiv.innerHTML = '<strong>验证失败！</strong><br>' + (data.errorMessage || '身份证号码格式不正确');
                }
                resultDiv.style.display = 'block';
            })
            .catch(error => {
                console.error('验证身份证失败:', error);
                resultDiv.className = 'validation-result invalid';
                resultDiv.innerHTML = '<strong>验证失败！</strong><br>网络错误: ' + error.message;
                resultDiv.style.display = 'block';
            });
        }

        // 显示进度条
        function showProgress(show) {
            const progressDiv = document.getElementById('updateProgress');
            if (show) {
                progressDiv.style.display = 'block';
                document.getElementById('progressFill').style.width = '50%';
                document.getElementById('progressText').textContent = '正在处理...';
            } else {
                progressDiv.style.display = 'none';
            }
        }

        // 显示成功消息
        function showSuccess(message) {
            const existingMsg = document.querySelector('.success');
            if (existingMsg) {
                existingMsg.remove();
            }
            
            const successDiv = document.createElement('div');
            successDiv.className = 'success';
            successDiv.textContent = message;
            document.querySelector('.container').insertBefore(successDiv, document.querySelector('.stats-summary'));
            
            setTimeout(() => {
                successDiv.remove();
            }, 5000);
        }

        // 显示错误消息
        function showError(message) {
            const existingMsg = document.querySelector('.error');
            if (existingMsg) {
                existingMsg.remove();
            }
            
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error';
            errorDiv.textContent = message;
            document.querySelector('.container').insertBefore(errorDiv, document.querySelector('.stats-summary'));
            
            setTimeout(() => {
                errorDiv.remove();
            }, 5000);
        }

        // 回车键验证身份证
        document.getElementById('idCardInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                validateIdCard();
            }
        });
    </script>
</body>
</html>
