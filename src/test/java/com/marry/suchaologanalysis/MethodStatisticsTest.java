package com.marry.suchaologanalysis;

import com.marry.suchaologanalysis.entity.LogRecord;
import com.marry.suchaologanalysis.entity.LogStatistics;
import com.marry.suchaologanalysis.mapper.LogRecordMapper;
import com.marry.suchaologanalysis.service.LogStatisticsService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 方法统计功能测试
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class MethodStatisticsTest {

    @Autowired
    private LogRecordMapper logRecordMapper;

    @Autowired
    private LogStatisticsService logStatisticsService;

    @BeforeEach
    void setUp() {
        // 清空表数据
        logRecordMapper.truncateTable();
        
        // 准备测试数据
        LocalDateTime baseTime = LocalDateTime.of(2025, 7, 3, 10, 0, 0);
        
        LogRecord record1 = new LogRecord();
        record1.setTimestamp(baseTime);
        record1.setPath("/api/user/login");
        record1.setMethod("login");
        record1.setStatus(1);
        record1.setTime(150L);
        record1.setMachineName("server1");
        record1.setClassName("UserController");
        
        LogRecord record2 = new LogRecord();
        record2.setTimestamp(baseTime.plusMinutes(30));
        record2.setPath("/api/user/login");
        record2.setMethod("login");
        record2.setStatus(1);
        record2.setTime(200L);
        record2.setMachineName("server1");
        record2.setClassName("UserController");
        
        LogRecord record3 = new LogRecord();
        record3.setTimestamp(baseTime.plusHours(1));
        record3.setPath("/api/order/create");
        record3.setMethod("createOrder");
        record3.setStatus(1);
        record3.setTime(300L);
        record3.setMachineName("server2");
        record3.setClassName("OrderController");
        
        LogRecord record4 = new LogRecord();
        record4.setTimestamp(baseTime.plusHours(1).plusMinutes(15));
        record4.setPath("/api/order/create");
        record4.setMethod("createOrder");
        record4.setStatus(0);
        record4.setTime(500L);
        record4.setMachineName("server2");
        record4.setClassName("OrderController");
        record4.setErrorMsg("订单创建失败");
        
        LogRecord record5 = new LogRecord();
        record5.setTimestamp(baseTime.plusHours(2));
        record5.setPath("/api/user/profile");
        record5.setMethod("getUserProfile");
        record5.setStatus(1);
        record5.setTime(100L);
        record5.setMachineName("server1");
        record5.setClassName("UserController");
        
        logRecordMapper.batchInsert(Arrays.asList(record1, record2, record3, record4, record5));
    }

    @Test
    void testGetMethodStatisticsByHour() {
        LocalDateTime startTime = LocalDateTime.of(2025, 7, 3, 9, 0, 0);
        LocalDateTime endTime = LocalDateTime.of(2025, 7, 3, 13, 0, 0);
        
        List<LogStatistics> statistics = logStatisticsService.getMethodStatisticsByHour(startTime, endTime);
        
        assertNotNull(statistics);
        assertFalse(statistics.isEmpty());
        
        // 验证包含预期的方法
        boolean hasLoginMethod = statistics.stream()
                .anyMatch(stat -> "login".equals(stat.getMethodName()));
        boolean hasCreateOrderMethod = statistics.stream()
                .anyMatch(stat -> "createOrder".equals(stat.getMethodName()));
        boolean hasGetUserProfileMethod = statistics.stream()
                .anyMatch(stat -> "getUserProfile".equals(stat.getMethodName()));
        
        assertTrue(hasLoginMethod, "应该包含login方法的统计");
        assertTrue(hasCreateOrderMethod, "应该包含createOrder方法的统计");
        assertTrue(hasGetUserProfileMethod, "应该包含getUserProfile方法的统计");
        
        // 验证login方法的统计数据
        LogStatistics loginStats = statistics.stream()
                .filter(stat -> "login".equals(stat.getMethodName()))
                .findFirst()
                .orElse(null);
        
        assertNotNull(loginStats);
        assertEquals(2L, loginStats.getTotalCount());
        assertEquals(2L, loginStats.getSuccessCount());
        assertEquals(0L, loginStats.getErrorCount());
        assertEquals(175.0, loginStats.getAvgResponseTime(), 0.1); // (150+200)/2
    }

    @Test
    void testGetMethodStatisticsByHourAndMachine() {
        LocalDateTime startTime = LocalDateTime.of(2025, 7, 3, 9, 0, 0);
        LocalDateTime endTime = LocalDateTime.of(2025, 7, 3, 13, 0, 0);
        
        List<LogStatistics> statistics = logStatisticsService.getMethodStatisticsByHourAndMachine(
                startTime, endTime, "server1");
        
        assertNotNull(statistics);
        assertFalse(statistics.isEmpty());
        
        // server1只应该有login和getUserProfile方法
        boolean hasLoginMethod = statistics.stream()
                .anyMatch(stat -> "login".equals(stat.getMethodName()));
        boolean hasGetUserProfileMethod = statistics.stream()
                .anyMatch(stat -> "getUserProfile".equals(stat.getMethodName()));
        boolean hasCreateOrderMethod = statistics.stream()
                .anyMatch(stat -> "createOrder".equals(stat.getMethodName()));
        
        assertTrue(hasLoginMethod, "server1应该包含login方法");
        assertTrue(hasGetUserProfileMethod, "server1应该包含getUserProfile方法");
        assertFalse(hasCreateOrderMethod, "server1不应该包含createOrder方法");
    }

    @Test
    void testGetMethodStatisticsByDay() {
        LocalDateTime startTime = LocalDateTime.of(2025, 7, 3, 0, 0, 0);
        LocalDateTime endTime = LocalDateTime.of(2025, 7, 3, 23, 59, 59);
        
        List<LogStatistics> statistics = logStatisticsService.getMethodStatisticsByDay(startTime, endTime);
        
        assertNotNull(statistics);
        assertFalse(statistics.isEmpty());
        
        // 验证包含预期的方法
        boolean hasLoginMethod = statistics.stream()
                .anyMatch(stat -> "login".equals(stat.getMethodName()));
        boolean hasCreateOrderMethod = statistics.stream()
                .anyMatch(stat -> "createOrder".equals(stat.getMethodName()));
        
        assertTrue(hasLoginMethod);
        assertTrue(hasCreateOrderMethod);
        
        // 验证createOrder方法的统计（包含一个错误）
        LogStatistics createOrderStats = statistics.stream()
                .filter(stat -> "createOrder".equals(stat.getMethodName()))
                .findFirst()
                .orElse(null);
        
        assertNotNull(createOrderStats);
        assertEquals(2L, createOrderStats.getTotalCount());
        assertEquals(1L, createOrderStats.getSuccessCount());
        assertEquals(1L, createOrderStats.getErrorCount());
        assertEquals(50.0, createOrderStats.getErrorRate(), 0.1); // 1/2 * 100
    }

    @Test
    void testGetAllMethods() {
        List<String> methods = logStatisticsService.getAllMethods();
        
        assertNotNull(methods);
        assertEquals(3, methods.size());
        assertTrue(methods.contains("login"));
        assertTrue(methods.contains("createOrder"));
        assertTrue(methods.contains("getUserProfile"));
    }

    @Test
    void testMethodStatisticsWithNullMethod() {
        // 添加一个method为null的记录
        LogRecord recordWithNullMethod = new LogRecord();
        recordWithNullMethod.setTimestamp(LocalDateTime.of(2025, 7, 3, 10, 0, 0));
        recordWithNullMethod.setPath("/api/test");
        recordWithNullMethod.setMethod(null);
        recordWithNullMethod.setStatus(1);
        recordWithNullMethod.setTime(100L);
        recordWithNullMethod.setMachineName("server1");
        
        logRecordMapper.batchInsert(Arrays.asList(recordWithNullMethod));
        
        LocalDateTime startTime = LocalDateTime.of(2025, 7, 3, 9, 0, 0);
        LocalDateTime endTime = LocalDateTime.of(2025, 7, 3, 13, 0, 0);
        
        List<LogStatistics> statistics = logStatisticsService.getMethodStatisticsByHour(startTime, endTime);
        
        // 确保null方法不会出现在统计中
        boolean hasNullMethod = statistics.stream()
                .anyMatch(stat -> stat.getMethodName() == null);
        
        assertFalse(hasNullMethod, "统计结果不应该包含null方法");
    }

    @Test
    void testGetMethodStatisticsBySecond() {
        // 添加更多同一秒内的数据用于测试
        LocalDateTime baseTime = LocalDateTime.of(2025, 7, 3, 10, 30, 15); // 精确到秒

        LogRecord record1 = new LogRecord();
        record1.setTimestamp(baseTime);
        record1.setPath("/api/user/login");
        record1.setMethod("login");
        record1.setStatus(1);
        record1.setTime(100L);
        record1.setMachineName("server1");

        LogRecord record2 = new LogRecord();
        record2.setTimestamp(baseTime); // 同一秒
        record2.setPath("/api/user/login");
        record2.setMethod("login");
        record2.setStatus(1);
        record2.setTime(200L);
        record2.setMachineName("server1");

        LogRecord record3 = new LogRecord();
        record3.setTimestamp(baseTime.plusSeconds(1)); // 下一秒
        record3.setPath("/api/user/login");
        record3.setMethod("login");
        record3.setStatus(0);
        record3.setTime(300L);
        record3.setMachineName("server1");
        record3.setErrorMsg("登录失败");

        logRecordMapper.batchInsert(Arrays.asList(record1, record2, record3));

        LocalDateTime startTime = baseTime.minusMinutes(1);
        LocalDateTime endTime = baseTime.plusMinutes(1);

        List<LogStatistics> statistics = logStatisticsService.getMethodStatisticsBySecond(
                startTime, endTime, "login");

        assertNotNull(statistics);
        assertEquals(2, statistics.size()); // 应该有两个时间点的数据

        // 验证第一个时间点的数据（两次调用在同一秒）
        LogStatistics firstSecondStats = statistics.stream()
                .filter(stat -> stat.getTimeLabel().contains("10:30:15"))
                .findFirst()
                .orElse(null);

        assertNotNull(firstSecondStats);
        assertEquals("login", firstSecondStats.getMethodName());
        assertEquals(2L, firstSecondStats.getTotalCount());
        assertEquals(2L, firstSecondStats.getSuccessCount());
        assertEquals(0L, firstSecondStats.getErrorCount());
        assertEquals(150.0, firstSecondStats.getAvgResponseTime(), 0.1); // (100+200)/2

        // 验证第二个时间点的数据（一次失败调用）
        LogStatistics secondSecondStats = statistics.stream()
                .filter(stat -> stat.getTimeLabel().contains("10:30:16"))
                .findFirst()
                .orElse(null);

        assertNotNull(secondSecondStats);
        assertEquals("login", secondSecondStats.getMethodName());
        assertEquals(1L, secondSecondStats.getTotalCount());
        assertEquals(0L, secondSecondStats.getSuccessCount());
        assertEquals(1L, secondSecondStats.getErrorCount());
        assertEquals(300.0, secondSecondStats.getAvgResponseTime(), 0.1);
        assertEquals(100.0, secondSecondStats.getErrorRate(), 0.1);
    }

    @Test
    void testGetMethodStatisticsBySecondAndMachine() {
        LocalDateTime baseTime = LocalDateTime.of(2025, 7, 3, 10, 30, 15);

        LogRecord record1 = new LogRecord();
        record1.setTimestamp(baseTime);
        record1.setPath("/api/user/login");
        record1.setMethod("login");
        record1.setStatus(1);
        record1.setTime(150L);
        record1.setMachineName("server1");

        LogRecord record2 = new LogRecord();
        record2.setTimestamp(baseTime);
        record2.setPath("/api/user/login");
        record2.setMethod("login");
        record2.setStatus(1);
        record2.setTime(200L);
        record2.setMachineName("server2"); // 不同机器

        logRecordMapper.batchInsert(Arrays.asList(record1, record2));

        LocalDateTime startTime = baseTime.minusMinutes(1);
        LocalDateTime endTime = baseTime.plusMinutes(1);

        // 只查询server1的数据
        List<LogStatistics> statistics = logStatisticsService.getMethodStatisticsBySecondAndMachine(
                startTime, endTime, "login", "server1");

        assertNotNull(statistics);
        assertEquals(1, statistics.size());

        LogStatistics stats = statistics.get(0);
        assertEquals("login", stats.getMethodName());
        assertEquals(1L, stats.getTotalCount()); // 只有server1的一条记录
        assertEquals(150.0, stats.getAvgResponseTime(), 0.1);
    }
}
