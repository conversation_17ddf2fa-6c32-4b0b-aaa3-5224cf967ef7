<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.marry.suchaologanalysis.mapper.LogRecordMapper">

    <!-- 批量插入日志记录 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO log_records (
            timestamp, path, ajax, status, error_code, error_msg, time,
            start_time, end_time, class_name, method, param, ip,
            net_user_id, machine_name, create_time
        ) VALUES
        <foreach collection="records" item="record" separator=",">
            (
                #{record.timestamp}, #{record.path}, #{record.ajax}, #{record.status},
                #{record.errorCode}, #{record.errorMsg}, #{record.time},
                #{record.startTime}, #{record.endTime}, #{record.className},
                #{record.method}, #{record.param}::jsonb, #{record.ip},
                #{record.netUserId}, #{record.machineName}, NOW()
            )
        </foreach>
    </insert>

    <!-- 根据时间范围查询日志记录 -->
    <select id="findByTimeRange" resultType="LogRecord">
        SELECT * FROM log_records
        WHERE timestamp BETWEEN #{startTime} AND #{endTime}
        ORDER BY timestamp DESC
    </select>

    <!-- 根据时间范围和机器名称查询日志记录 -->
    <select id="findByTimeRangeAndMachine" resultType="LogRecord">
        SELECT * FROM log_records
        WHERE timestamp BETWEEN #{startTime} AND #{endTime}
        AND machine_name = #{machineName}
        ORDER BY timestamp DESC
    </select>

    <!-- 按小时统计日志数据 -->
    <select id="getHourlyStatistics" resultType="LogStatistics">
        SELECT
            TO_CHAR(DATE_TRUNC('hour', timestamp), 'YYYY-MM-DD HH24:00:00') as timeLabel,
            DATE_TRUNC('hour', timestamp) as timePoint,
            COUNT(*) as totalCount,
            SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as successCount,
            SUM(CASE WHEN status != 1 THEN 1 ELSE 0 END) as errorCount,
            AVG(time) as avgResponseTime,
            MAX(time) as maxResponseTime,
            MIN(time) as minResponseTime,
            (SUM(CASE WHEN status != 1 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) as errorRate
        FROM log_records
        WHERE timestamp BETWEEN #{startTime} AND #{endTime}
        GROUP BY DATE_TRUNC('hour', timestamp)
        ORDER BY timePoint
    </select>

    <!-- 按小时统计日志数据（指定机器） -->
    <select id="getHourlyStatisticsByMachine" resultType="LogStatistics">
        SELECT
            TO_CHAR(timestamp, 'YYYY-MM-DD HH24:00:00') as timeLabel,
            DATE_TRUNC('hour', timestamp) as timePoint,
            COUNT(*) as totalCount,
            SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as successCount,
            SUM(CASE WHEN status != 1 THEN 1 ELSE 0 END) as errorCount,
            AVG(time) as avgResponseTime,
            MAX(time) as maxResponseTime,
            MIN(time) as minResponseTime,
            (SUM(CASE WHEN status != 1 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) as errorRate
        FROM log_records
        WHERE timestamp BETWEEN #{startTime} AND #{endTime}
        AND machine_name = #{machineName}
        GROUP BY DATE_TRUNC('hour', timestamp)
        ORDER BY timePoint
    </select>

    <!-- 按天统计日志数据 -->
    <select id="getDailyStatistics" resultType="LogStatistics">
        SELECT
            TO_CHAR(timestamp, 'YYYY-MM-DD') as timeLabel,
            DATE_TRUNC('day', timestamp) as timePoint,
            COUNT(*) as totalCount,
            SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as successCount,
            SUM(CASE WHEN status != 1 THEN 1 ELSE 0 END) as errorCount,
            AVG(time) as avgResponseTime,
            MAX(time) as maxResponseTime,
            MIN(time) as minResponseTime,
            (SUM(CASE WHEN status != 1 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) as errorRate
        FROM log_records
        WHERE timestamp BETWEEN #{startTime} AND #{endTime}
        GROUP BY DATE_TRUNC('day', timestamp)
        ORDER BY timePoint
    </select>

    <!-- 获取错误统计 -->
    <select id="getErrorStatistics" resultType="LogStatistics">
        SELECT
            error_code as timeLabel,
            COUNT(*) as totalCount,
            error_msg as timeLabel
        FROM log_records
        WHERE timestamp BETWEEN #{startTime} AND #{endTime}
        AND status != 1
        GROUP BY error_code, error_msg
        ORDER BY totalCount DESC
    </select>

    <!-- 获取响应时间统计 -->
    <select id="getResponseTimeStatistics" resultType="LogStatistics">
        SELECT
            CASE
                WHEN time &lt; 100 THEN '&lt;100ms'
                WHEN time &lt; 500 THEN '100-500ms'
                WHEN time &lt; 1000 THEN '500ms-1s'
                WHEN time &lt; 3000 THEN '1-3s'
                ELSE '&gt;3s'
            END as timeLabel,
            COUNT(*) as totalCount
        FROM log_records
        WHERE timestamp BETWEEN #{startTime} AND #{endTime}
        GROUP BY
            CASE
                WHEN time &lt; 100 THEN '&lt;100ms'
                WHEN time &lt; 500 THEN '100-500ms'
                WHEN time &lt; 1000 THEN '500ms-1s'
                WHEN time &lt; 3000 THEN '1-3s'
                ELSE '&gt;3s'
            END
        ORDER BY MIN(time)
    </select>

    <!-- 获取总记录数 -->
    <select id="getTotalCount" resultType="java.lang.Long">
        SELECT COUNT(*) FROM log_records
    </select>

    <!-- 清空表数据 -->
    <delete id="truncateTable">
        TRUNCATE TABLE log_records
    </delete>

    <!-- 获取所有机器名称列表 -->
    <select id="getAllMachineNames" resultType="java.lang.String">
        SELECT DISTINCT machine_name
        FROM log_records
        WHERE machine_name IS NOT NULL
        ORDER BY machine_name
    </select>

</mapper>
