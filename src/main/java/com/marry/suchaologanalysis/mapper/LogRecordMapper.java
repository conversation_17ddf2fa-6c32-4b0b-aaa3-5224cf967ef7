package com.marry.suchaologanalysis.mapper;

import com.marry.suchaologanalysis.entity.LogRecord;
import com.marry.suchaologanalysis.entity.LogStatistics;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 日志记录Mapper接口
 */
@Mapper
public interface LogRecordMapper {
    
    /**
     * 批量插入日志记录
     */
    void batchInsert(@Param("records") List<LogRecord> records);
    
    /**
     * 根据时间范围查询日志记录
     */
    List<LogRecord> findByTimeRange(@Param("startTime") LocalDateTime startTime, 
                                   @Param("endTime") LocalDateTime endTime);
    
    /**
     * 按小时统计日志数据
     */
    List<LogStatistics> getHourlyStatistics(@Param("startTime") LocalDateTime startTime, 
                                          @Param("endTime") LocalDateTime endTime);
    
    /**
     * 按天统计日志数据
     */
    List<LogStatistics> getDailyStatistics(@Param("startTime") LocalDateTime startTime, 
                                         @Param("endTime") LocalDateTime endTime);
    
    /**
     * 获取错误统计
     */
    List<LogStatistics> getErrorStatistics(@Param("startTime") LocalDateTime startTime, 
                                         @Param("endTime") LocalDateTime endTime);
    
    /**
     * 获取响应时间统计
     */
    List<LogStatistics> getResponseTimeStatistics(@Param("startTime") LocalDateTime startTime, 
                                                 @Param("endTime") LocalDateTime endTime);
    
    /**
     * 获取总记录数
     */
    Long getTotalCount();
    
    /**
     * 清空表数据
     */
    void truncateTable();
}
