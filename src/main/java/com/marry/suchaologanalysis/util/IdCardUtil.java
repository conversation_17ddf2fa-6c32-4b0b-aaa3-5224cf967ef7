package com.marry.suchaologanalysis.util;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * 身份证号码解析工具类
 */
@Slf4j
public class IdCardUtil {

    // 身份证号码正则表达式（18位）
    private static final Pattern ID_CARD_PATTERN = Pattern.compile("^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$");
    
    // 15位身份证号码正则表达式
    private static final Pattern ID_CARD_15_PATTERN = Pattern.compile("^[1-9]\\d{5}\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}$");

    // 地区代码映射表（前6位）
    private static final Map<String, RegionInfo> REGION_CODE_MAP = new HashMap<>();

    static {
        initRegionCodeMap();
    }

    /**
     * 身份证信息解析结果
     */
    @Data
    public static class IdCardInfo {
        private boolean valid;          // 是否有效
        private String province;        // 省份
        private String city;           // 城市
        private Integer age;           // 年龄
        private String gender;         // 性别
        private LocalDate birthDate;   // 出生日期
        private String errorMessage;   // 错误信息
    }

    /**
     * 地区信息
     */
    @Data
    private static class RegionInfo {
        private String province;
        private String city;

        public RegionInfo(String province, String city) {
            this.province = province;
            this.city = city;
        }
    }

    /**
     * 解析身份证号码
     */
    public static IdCardInfo parseIdCard(String idCard) {
        IdCardInfo info = new IdCardInfo();
        
        if (idCard == null || idCard.trim().isEmpty()) {
            info.setValid(false);
            info.setErrorMessage("身份证号码不能为空");
            return info;
        }

        idCard = idCard.trim().toUpperCase();

        // 验证格式
        if (!isValidFormat(idCard)) {
            info.setValid(false);
            info.setErrorMessage("身份证号码格式不正确");
            return info;
        }

        try {
            // 如果是15位，转换为18位
            if (idCard.length() == 15) {
                idCard = convert15To18(idCard);
            }

            // 解析地区信息
            String regionCode = idCard.substring(0, 6);
            RegionInfo regionInfo = getRegionInfo(regionCode);
            if (regionInfo != null) {
                info.setProvince(regionInfo.getProvince());
                info.setCity(regionInfo.getCity());
            }

            // 解析出生日期和年龄
            String birthStr = idCard.substring(6, 14);
            LocalDate birthDate = LocalDate.parse(birthStr, DateTimeFormatter.ofPattern("yyyyMMdd"));
            info.setBirthDate(birthDate);
            
            // 计算年龄
            int age = LocalDate.now().getYear() - birthDate.getYear();
            if (LocalDate.now().getDayOfYear() < birthDate.getDayOfYear()) {
                age--;
            }
            info.setAge(age);

            // 解析性别
            int genderCode = Integer.parseInt(idCard.substring(16, 17));
            info.setGender(genderCode % 2 == 0 ? "女" : "男");

            info.setValid(true);
            
        } catch (Exception e) {
            log.warn("解析身份证号码失败: {}, 错误: {}", idCard, e.getMessage());
            info.setValid(false);
            info.setErrorMessage("身份证号码解析失败: " + e.getMessage());
        }

        return info;
    }

    /**
     * 验证身份证号码格式
     */
    private static boolean isValidFormat(String idCard) {
        if (idCard.length() == 18) {
            return ID_CARD_PATTERN.matcher(idCard).matches();
        } else if (idCard.length() == 15) {
            return ID_CARD_15_PATTERN.matcher(idCard).matches();
        }
        return false;
    }

    /**
     * 15位身份证转18位
     */
    private static String convert15To18(String idCard15) {
        if (idCard15.length() != 15) {
            return idCard15;
        }
        
        String idCard17 = idCard15.substring(0, 6) + "19" + idCard15.substring(6);
        String checkCode = getCheckCode(idCard17);
        return idCard17 + checkCode;
    }

    /**
     * 计算校验码
     */
    private static String getCheckCode(String idCard17) {
        int[] factor = {7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2};
        String[] checkCodes = {"1", "0", "X", "9", "8", "7", "6", "5", "4", "3", "2"};
        
        int sum = 0;
        for (int i = 0; i < 17; i++) {
            sum += Integer.parseInt(String.valueOf(idCard17.charAt(i))) * factor[i];
        }
        
        return checkCodes[sum % 11];
    }

    /**
     * 根据地区代码获取地区信息
     */
    private static RegionInfo getRegionInfo(String regionCode) {
        // 先尝试完整匹配
        RegionInfo info = REGION_CODE_MAP.get(regionCode);
        if (info != null) {
            return info;
        }
        
        // 尝试匹配省级代码（前2位）
        String provinceCode = regionCode.substring(0, 2) + "0000";
        info = REGION_CODE_MAP.get(provinceCode);
        if (info != null) {
            // 尝试匹配市级代码（前4位）
            String cityCode = regionCode.substring(0, 4) + "00";
            RegionInfo cityInfo = REGION_CODE_MAP.get(cityCode);
            if (cityInfo != null) {
                return cityInfo;
            }
            return info;
        }
        
        return null;
    }

    /**
     * 初始化地区代码映射表
     */
    private static void initRegionCodeMap() {
        // 直辖市
        REGION_CODE_MAP.put("110000", new RegionInfo("北京市", "北京市"));
        REGION_CODE_MAP.put("110100", new RegionInfo("北京市", "北京市"));
        REGION_CODE_MAP.put("120000", new RegionInfo("天津市", "天津市"));
        REGION_CODE_MAP.put("120100", new RegionInfo("天津市", "天津市"));
        REGION_CODE_MAP.put("310000", new RegionInfo("上海市", "上海市"));
        REGION_CODE_MAP.put("310100", new RegionInfo("上海市", "上海市"));
        REGION_CODE_MAP.put("500000", new RegionInfo("重庆市", "重庆市"));
        REGION_CODE_MAP.put("500100", new RegionInfo("重庆市", "重庆市"));

        // 河北省
        REGION_CODE_MAP.put("130000", new RegionInfo("河北省", ""));
        REGION_CODE_MAP.put("130100", new RegionInfo("河北省", "石家庄市"));
        REGION_CODE_MAP.put("130200", new RegionInfo("河北省", "唐山市"));
        REGION_CODE_MAP.put("130300", new RegionInfo("河北省", "秦皇岛市"));
        REGION_CODE_MAP.put("130400", new RegionInfo("河北省", "邯郸市"));
        REGION_CODE_MAP.put("130500", new RegionInfo("河北省", "邢台市"));
        REGION_CODE_MAP.put("130600", new RegionInfo("河北省", "保定市"));
        REGION_CODE_MAP.put("130700", new RegionInfo("河北省", "张家口市"));
        REGION_CODE_MAP.put("130800", new RegionInfo("河北省", "承德市"));
        REGION_CODE_MAP.put("130900", new RegionInfo("河北省", "沧州市"));
        REGION_CODE_MAP.put("131000", new RegionInfo("河北省", "廊坊市"));
        REGION_CODE_MAP.put("131100", new RegionInfo("河北省", "衡水市"));

        // 山西省
        REGION_CODE_MAP.put("140000", new RegionInfo("山西省", ""));
        REGION_CODE_MAP.put("140100", new RegionInfo("山西省", "太原市"));
        REGION_CODE_MAP.put("140200", new RegionInfo("山西省", "大同市"));
        REGION_CODE_MAP.put("140300", new RegionInfo("山西省", "阳泉市"));
        REGION_CODE_MAP.put("140400", new RegionInfo("山西省", "长治市"));
        REGION_CODE_MAP.put("140500", new RegionInfo("山西省", "晋城市"));
        REGION_CODE_MAP.put("140600", new RegionInfo("山西省", "朔州市"));
        REGION_CODE_MAP.put("140700", new RegionInfo("山西省", "晋中市"));
        REGION_CODE_MAP.put("140800", new RegionInfo("山西省", "运城市"));
        REGION_CODE_MAP.put("140900", new RegionInfo("山西省", "忻州市"));
        REGION_CODE_MAP.put("141000", new RegionInfo("山西省", "临汾市"));
        REGION_CODE_MAP.put("141100", new RegionInfo("山西省", "吕梁市"));

        // 内蒙古自治区
        REGION_CODE_MAP.put("150000", new RegionInfo("内蒙古自治区", ""));
        REGION_CODE_MAP.put("150100", new RegionInfo("内蒙古自治区", "呼和浩特市"));
        REGION_CODE_MAP.put("150200", new RegionInfo("内蒙古自治区", "包头市"));
        REGION_CODE_MAP.put("150300", new RegionInfo("内蒙古自治区", "乌海市"));
        REGION_CODE_MAP.put("150400", new RegionInfo("内蒙古自治区", "赤峰市"));
        REGION_CODE_MAP.put("150500", new RegionInfo("内蒙古自治区", "通辽市"));
        REGION_CODE_MAP.put("150600", new RegionInfo("内蒙古自治区", "鄂尔多斯市"));
        REGION_CODE_MAP.put("150700", new RegionInfo("内蒙古自治区", "呼伦贝尔市"));
        REGION_CODE_MAP.put("150800", new RegionInfo("内蒙古自治区", "巴彦淖尔市"));
        REGION_CODE_MAP.put("150900", new RegionInfo("内蒙古自治区", "乌兰察布市"));

        // 辽宁省
        REGION_CODE_MAP.put("210000", new RegionInfo("辽宁省", ""));
        REGION_CODE_MAP.put("210100", new RegionInfo("辽宁省", "沈阳市"));
        REGION_CODE_MAP.put("210200", new RegionInfo("辽宁省", "大连市"));
        REGION_CODE_MAP.put("210300", new RegionInfo("辽宁省", "鞍山市"));
        REGION_CODE_MAP.put("210400", new RegionInfo("辽宁省", "抚顺市"));
        REGION_CODE_MAP.put("210500", new RegionInfo("辽宁省", "本溪市"));
        REGION_CODE_MAP.put("210600", new RegionInfo("辽宁省", "丹东市"));
        REGION_CODE_MAP.put("210700", new RegionInfo("辽宁省", "锦州市"));
        REGION_CODE_MAP.put("210800", new RegionInfo("辽宁省", "营口市"));
        REGION_CODE_MAP.put("210900", new RegionInfo("辽宁省", "阜新市"));
        REGION_CODE_MAP.put("211000", new RegionInfo("辽宁省", "辽阳市"));
        REGION_CODE_MAP.put("211100", new RegionInfo("辽宁省", "盘锦市"));
        REGION_CODE_MAP.put("211200", new RegionInfo("辽宁省", "铁岭市"));
        REGION_CODE_MAP.put("211300", new RegionInfo("辽宁省", "朝阳市"));
        REGION_CODE_MAP.put("211400", new RegionInfo("辽宁省", "葫芦岛市"));

        // 吉林省
        REGION_CODE_MAP.put("220000", new RegionInfo("吉林省", ""));
        REGION_CODE_MAP.put("220100", new RegionInfo("吉林省", "长春市"));
        REGION_CODE_MAP.put("220200", new RegionInfo("吉林省", "吉林市"));
        REGION_CODE_MAP.put("220300", new RegionInfo("吉林省", "四平市"));
        REGION_CODE_MAP.put("220400", new RegionInfo("吉林省", "辽源市"));
        REGION_CODE_MAP.put("220500", new RegionInfo("吉林省", "通化市"));
        REGION_CODE_MAP.put("220600", new RegionInfo("吉林省", "白山市"));
        REGION_CODE_MAP.put("220700", new RegionInfo("吉林省", "松原市"));
        REGION_CODE_MAP.put("220800", new RegionInfo("吉林省", "白城市"));

        // 黑龙江省
        REGION_CODE_MAP.put("230000", new RegionInfo("黑龙江省", ""));
        REGION_CODE_MAP.put("230100", new RegionInfo("黑龙江省", "哈尔滨市"));
        REGION_CODE_MAP.put("230200", new RegionInfo("黑龙江省", "齐齐哈尔市"));
        REGION_CODE_MAP.put("230300", new RegionInfo("黑龙江省", "鸡西市"));
        REGION_CODE_MAP.put("230400", new RegionInfo("黑龙江省", "鹤岗市"));
        REGION_CODE_MAP.put("230500", new RegionInfo("黑龙江省", "双鸭山市"));
        REGION_CODE_MAP.put("230600", new RegionInfo("黑龙江省", "大庆市"));
        REGION_CODE_MAP.put("230700", new RegionInfo("黑龙江省", "伊春市"));
        REGION_CODE_MAP.put("230800", new RegionInfo("黑龙江省", "佳木斯市"));
        REGION_CODE_MAP.put("230900", new RegionInfo("黑龙江省", "七台河市"));
        REGION_CODE_MAP.put("231000", new RegionInfo("黑龙江省", "牡丹江市"));
        REGION_CODE_MAP.put("231100", new RegionInfo("黑龙江省", "黑河市"));
        REGION_CODE_MAP.put("231200", new RegionInfo("黑龙江省", "绥化市"));

        // 江苏省
        REGION_CODE_MAP.put("320000", new RegionInfo("江苏省", ""));
        REGION_CODE_MAP.put("320100", new RegionInfo("江苏省", "南京市"));
        REGION_CODE_MAP.put("320200", new RegionInfo("江苏省", "无锡市"));
        REGION_CODE_MAP.put("320300", new RegionInfo("江苏省", "徐州市"));
        REGION_CODE_MAP.put("320400", new RegionInfo("江苏省", "常州市"));
        REGION_CODE_MAP.put("320500", new RegionInfo("江苏省", "苏州市"));
        REGION_CODE_MAP.put("320600", new RegionInfo("江苏省", "南通市"));
        REGION_CODE_MAP.put("320700", new RegionInfo("江苏省", "连云港市"));
        REGION_CODE_MAP.put("320800", new RegionInfo("江苏省", "淮安市"));
        REGION_CODE_MAP.put("320900", new RegionInfo("江苏省", "盐城市"));
        REGION_CODE_MAP.put("321000", new RegionInfo("江苏省", "扬州市"));
        REGION_CODE_MAP.put("321100", new RegionInfo("江苏省", "镇江市"));
        REGION_CODE_MAP.put("321200", new RegionInfo("江苏省", "泰州市"));
        REGION_CODE_MAP.put("321300", new RegionInfo("江苏省", "宿迁市"));

        // 浙江省
        REGION_CODE_MAP.put("330000", new RegionInfo("浙江省", ""));
        REGION_CODE_MAP.put("330100", new RegionInfo("浙江省", "杭州市"));
        REGION_CODE_MAP.put("330200", new RegionInfo("浙江省", "宁波市"));
        REGION_CODE_MAP.put("330300", new RegionInfo("浙江省", "温州市"));
        REGION_CODE_MAP.put("330400", new RegionInfo("浙江省", "嘉兴市"));
        REGION_CODE_MAP.put("330500", new RegionInfo("浙江省", "湖州市"));
        REGION_CODE_MAP.put("330600", new RegionInfo("浙江省", "绍兴市"));
        REGION_CODE_MAP.put("330700", new RegionInfo("浙江省", "金华市"));
        REGION_CODE_MAP.put("330800", new RegionInfo("浙江省", "衢州市"));
        REGION_CODE_MAP.put("330900", new RegionInfo("浙江省", "舟山市"));
        REGION_CODE_MAP.put("331000", new RegionInfo("浙江省", "台州市"));
        REGION_CODE_MAP.put("331100", new RegionInfo("浙江省", "丽水市"));

        // 安徽省
        REGION_CODE_MAP.put("340000", new RegionInfo("安徽省", ""));
        REGION_CODE_MAP.put("340100", new RegionInfo("安徽省", "合肥市"));
        REGION_CODE_MAP.put("340200", new RegionInfo("安徽省", "芜湖市"));
        REGION_CODE_MAP.put("340300", new RegionInfo("安徽省", "蚌埠市"));
        REGION_CODE_MAP.put("340400", new RegionInfo("安徽省", "淮南市"));
        REGION_CODE_MAP.put("340500", new RegionInfo("安徽省", "马鞍山市"));
        REGION_CODE_MAP.put("340600", new RegionInfo("安徽省", "淮北市"));
        REGION_CODE_MAP.put("340700", new RegionInfo("安徽省", "铜陵市"));
        REGION_CODE_MAP.put("340800", new RegionInfo("安徽省", "安庆市"));
        REGION_CODE_MAP.put("341000", new RegionInfo("安徽省", "黄山市"));
        REGION_CODE_MAP.put("341100", new RegionInfo("安徽省", "滁州市"));
        REGION_CODE_MAP.put("341200", new RegionInfo("安徽省", "阜阳市"));
        REGION_CODE_MAP.put("341300", new RegionInfo("安徽省", "宿州市"));
        REGION_CODE_MAP.put("341500", new RegionInfo("安徽省", "六安市"));
        REGION_CODE_MAP.put("341600", new RegionInfo("安徽省", "亳州市"));
        REGION_CODE_MAP.put("341700", new RegionInfo("安徽省", "池州市"));
        REGION_CODE_MAP.put("341800", new RegionInfo("安徽省", "宣城市"));
    }
}
