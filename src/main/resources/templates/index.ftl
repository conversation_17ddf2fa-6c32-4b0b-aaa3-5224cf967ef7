<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日志分析系统</title>
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/date-fns@2.29.3/index.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #eee;
        }
        .stats-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-card h3 {
            margin: 0 0 10px 0;
            font-size: 14px;
            opacity: 0.9;
        }
        .stat-card .value {
            font-size: 24px;
            font-weight: bold;
        }
        .controls {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
            align-items: center;
        }
        .control-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        .control-group label {
            font-weight: bold;
            font-size: 12px;
            color: #666;
        }
        input, select, button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            cursor: pointer;
            transition: background 0.3s;
        }
        button:hover {
            background: #0056b3;
        }
        .chart-container {
            margin-bottom: 40px;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .chart-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        .chart-wrapper {
            position: relative;
            height: 400px;
        }
        .nav-links {
            text-align: center;
            margin-bottom: 20px;
        }
        .nav-links a {
            display: inline-block;
            margin: 0 10px;
            padding: 10px 20px;
            background: #28a745;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            transition: background 0.3s;
        }
        .nav-links a:hover {
            background: #1e7e34;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>日志分析系统</h1>
            <p>实时监控和分析系统日志数据</p>
        </div>

        <div class="nav-links">
            <a href="/log/import">导入日志</a>
            <a href="#" onclick="clearLogs()">清空数据</a>
            <a href="/ticket-person">票务管理</a>
            <a href="/ticket-report">票务报表</a>
        </div>

        <div class="stats-summary">
            <div class="stat-card">
                <h3>总日志数</h3>
                <div class="value">${totalCount!0}</div>
            </div>
            <div class="stat-card">
                <h3>机器数量</h3>
                <div class="value">${machineNames?size}</div>
            </div>
            <div class="stat-card">
                <h3>API路径数</h3>
                <div class="value">${paths?size}</div>
            </div>
            <div class="stat-card">
                <h3>方法数量</h3>
                <div class="value">${methods?size}</div>
            </div>
        </div>

        <div class="controls">
            <div class="control-group">
                <label>开始时间</label>
                <input type="datetime-local" id="startTime" value="">
            </div>
            <div class="control-group">
                <label>结束时间</label>
                <input type="datetime-local" id="endTime" value="">
            </div>
            <div class="control-group">
                <label>机器名称</label>
                <select id="machineName">
                    <option value="">全部机器</option>
                    <#list machineNames as machine>
                        <option value="${machine}">${machine}</option>
                    </#list>
                </select>
            </div>
            <div class="control-group">
                <label>方法名称</label>
                <select id="methodName">
                    <option value="">选择方法</option>
                    <#list methods as method>
                        <option value="${method}">${method}</option>
                    </#list>
                </select>
            </div>
            <div class="control-group">
                <label>&nbsp;</label>
                <button onclick="loadCharts()">刷新图表</button>
            </div>
            <div class="control-group">
                <label>&nbsp;</label>
                <button onclick="loadMethodDetailChart()">方法详细分析</button>
            </div>
        </div>

        <div class="chart-container">
            <div class="chart-title">请求量趋势（按小时）</div>
            <div class="chart-wrapper">
                <canvas id="hourlyChart"></canvas>
            </div>
        </div>

        <div class="chart-container">
            <div class="chart-title">响应时间分布</div>
            <div class="chart-wrapper">
                <canvas id="responseTimeChart"></canvas>
            </div>
        </div>

        <div class="chart-container">
            <div class="chart-title">错误统计</div>
            <div class="chart-wrapper">
                <canvas id="errorChart"></canvas>
            </div>
        </div>

        <div class="chart-container">
            <div class="chart-title">API路径访问统计（Top 20）</div>
            <div class="chart-wrapper">
                <canvas id="pathChart"></canvas>
            </div>
        </div>

        <div class="chart-container">
            <div class="chart-title">方法调用统计（按小时）</div>
            <div class="chart-wrapper">
                <canvas id="methodChart"></canvas>
            </div>
        </div>

        <div class="chart-container" id="methodDetailContainer" style="display: none;">
            <div class="chart-title">方法调用详细分析（按秒级 - 平均响应时间）</div>
            <div class="chart-wrapper">
                <canvas id="methodDetailChart"></canvas>
            </div>
        </div>
    </div>

    <script>
        let hourlyChart, responseTimeChart, errorChart, pathChart, methodChart, methodDetailChart;

        // 初始化时间选择器
        function initTimeSelectors() {
            const now = new Date();
            const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);

            document.getElementById('startTime').value = formatDateTime(yesterday);
            document.getElementById('endTime').value = formatDateTime(now);
        }

        function formatDateTime(date) {
            return date.toISOString().slice(0, 16);
        }

        // 加载图表数据
        async function loadCharts() {
            const startTime = document.getElementById('startTime').value;
            const endTime = document.getElementById('endTime').value;
            const machineName = document.getElementById('machineName').value;

            if (!startTime || !endTime) {
                alert('请选择时间范围');
                return;
            }

            try {
                await Promise.all([
                    loadHourlyChart(startTime, endTime, machineName),
                    loadResponseTimeChart(startTime, endTime),
                    loadErrorChart(startTime, endTime),
                    loadPathChart(startTime, endTime, machineName),
                    loadMethodChart(startTime, endTime, machineName)
                ]);
            } catch (error) {
                console.error('加载图表失败:', error);
                alert('加载图表失败，请检查网络连接');
            }
        }

        // 加载小时趋势图
        async function loadHourlyChart(startTime, endTime, machineName) {
            const hourlyParams = new URLSearchParams({
                startTime: startTime.replace('T', ' ') + ':00',
                endTime: endTime.replace('T', ' ') + ':00'
            });

            if (machineName) {
                hourlyParams.append('machineName', machineName);
            }

            const hourlyResponse = await fetch('/log/statistics/hourly?' + hourlyParams);
            const hourlyData = await hourlyResponse.json();

            const hourlyCtx = document.getElementById('hourlyChart').getContext('2d');

            if (hourlyChart) {
                hourlyChart.destroy();
            }

            hourlyChart = new Chart(hourlyCtx, {
                type: 'line',
                data: {
                    labels: hourlyData.map(item => item.timeLabel),
                    datasets: [{
                        label: '总请求数',
                        data: hourlyData.map(item => item.totalCount),
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        tension: 0.1
                    }, {
                        label: '成功请求数',
                        data: hourlyData.map(item => item.successCount),
                        borderColor: 'rgb(54, 162, 235)',
                        backgroundColor: 'rgba(54, 162, 235, 0.2)',
                        tension: 0.1
                    }, {
                        label: '错误请求数',
                        data: hourlyData.map(item => item.errorCount),
                        borderColor: 'rgb(255, 99, 132)',
                        backgroundColor: 'rgba(255, 99, 132, 0.2)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        // 加载响应时间分布图
        async function loadResponseTimeChart(startTime, endTime) {
            const responseTimeParams = new URLSearchParams({
                startTime: startTime.replace('T', ' ') + ':00',
                endTime: endTime.replace('T', ' ') + ':00'
            });

            const responseTimeResponse = await fetch('/log/statistics/response-time?' + responseTimeParams);
            const responseTimeData = await responseTimeResponse.json();

            const responseTimeCtx = document.getElementById('responseTimeChart').getContext('2d');

            if (responseTimeChart) {
                responseTimeChart.destroy();
            }

            responseTimeChart = new Chart(responseTimeCtx, {
                type: 'doughnut',
                data: {
                    labels: responseTimeData.map(item => item.timeLabel),
                    datasets: [{
                        data: responseTimeData.map(item => item.totalCount),
                        backgroundColor: [
                            '#FF6384',
                            '#36A2EB',
                            '#FFCE56',
                            '#4BC0C0',
                            '#9966FF'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
        }

        // 加载错误统计图
        async function loadErrorChart(startTime, endTime) {
            const errorParams = new URLSearchParams({
                startTime: startTime.replace('T', ' ') + ':00',
                endTime: endTime.replace('T', ' ') + ':00'
            });

            const errorResponse = await fetch('/log/statistics/errors?' + errorParams);
            const errorData = await errorResponse.json();

            const errorCtx = document.getElementById('errorChart').getContext('2d');

            if (errorChart) {
                errorChart.destroy();
            }

            errorChart = new Chart(errorCtx, {
                type: 'bar',
                data: {
                    labels: errorData.map(item => item.timeLabel || '未知错误'),
                    datasets: [{
                        label: '错误次数',
                        data: errorData.map(item => item.totalCount),
                        backgroundColor: 'rgba(255, 99, 132, 0.8)',
                        borderColor: 'rgba(255, 99, 132, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        // 清空日志
        async function clearLogs() {
            if (!confirm('确定要清空所有日志数据吗？此操作不可恢复！')) {
                return;
            }

            try {
                const response = await fetch('/log/clear', {
                    method: 'POST'
                });
                const result = await response.json();

                if (result.success) {
                    alert('日志数据已清空');
                    location.reload();
                } else {
                    alert('清空失败: ' + result.message);
                }
            } catch (error) {
                console.error('清空失败:', error);
                alert('清空失败，请检查网络连接');
            }
        }

        // 加载路径统计图
        async function loadPathChart(startTime, endTime, machineName) {
            const pathParams = new URLSearchParams({
                startTime: startTime.replace('T', ' ') + ':00',
                endTime: endTime.replace('T', ' ') + ':00'
            });

            if (machineName) {
                pathParams.append('machineName', machineName);
            }

            const pathResponse = await fetch('/log/statistics/path?' + pathParams);
            const pathData = await pathResponse.json();

            const pathCtx = document.getElementById('pathChart').getContext('2d');

            if (pathChart) {
                pathChart.destroy();
            }

            pathChart = new Chart(pathCtx, {
                type: 'bar',
                data: {
                    labels: pathData.map(item => {
                        // 截断过长的路径名
                        const path = item.timeLabel || '未知路径';
                        return path.length > 40 ? path.substring(0, 37) + '...' : path;
                    }),
                    datasets: [{
                        label: '请求次数',
                        data: pathData.map(item => item.totalCount),
                        backgroundColor: 'rgba(54, 162, 235, 0.6)',
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    indexAxis: 'y',
                    scales: {
                        x: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '请求次数'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'API路径'
                            }
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: 'API路径访问统计 (Top 20)'
                        },
                        tooltip: {
                            callbacks: {
                                title: function(context) {
                                    const index = context[0].dataIndex;
                                    return pathData[index].timeLabel || '未知路径';
                                },
                                afterBody: function(context) {
                                    const index = context[0].dataIndex;
                                    const item = pathData[index];
                                    return [
                                        '成功次数: ' + (item.successCount || 0),
                                        '错误次数: ' + (item.errorCount || 0),
                                        '错误率: ' + (item.errorRate ? item.errorRate.toFixed(2) + '%' : '0%'),
                                        '平均响应时间: ' + (item.avgResponseTime ? Math.round(item.avgResponseTime) : 0) + 'ms',
                                        '最大响应时间: ' + (item.maxResponseTime || 0) + 'ms'
                                    ];
                                }
                            }
                        }
                    }
                }
            });
        }

        // 加载方法统计图
        async function loadMethodChart(startTime, endTime, machineName) {
            const methodParams = new URLSearchParams({
                startTime: startTime.replace('T', ' ') + ':00',
                endTime: endTime.replace('T', ' ') + ':00'
            });

            if (machineName) {
                methodParams.append('machineName', machineName);
            }

            const methodResponse = await fetch('/log/statistics/method/hourly?' + methodParams);
            const methodData = await methodResponse.json();

            // 按方法分组数据
            const methodGroups = {};
            methodData.forEach(item => {
                if (!methodGroups[item.methodName]) {
                    methodGroups[item.methodName] = {
                        labels: [],
                        totalCounts: [],
                        avgResponseTimes: []
                    };
                }
                methodGroups[item.methodName].labels.push(item.timeLabel);
                methodGroups[item.methodName].totalCounts.push(item.totalCount);
                methodGroups[item.methodName].avgResponseTimes.push(Math.round(item.avgResponseTime || 0));
            });

            const methodCtx = document.getElementById('methodChart').getContext('2d');

            if (methodChart) {
                methodChart.destroy();
            }

            // 获取前10个最活跃的方法
            const topMethods = Object.keys(methodGroups)
                .map(method => ({
                    method: method,
                    totalCalls: methodGroups[method].totalCounts.reduce((a, b) => a + b, 0)
                }))
                .sort((a, b) => b.totalCalls - a.totalCalls)
                .slice(0, 10)
                .map(item => item.method);

            // 生成颜色
            const colors = [
                'rgba(255, 99, 132, 0.6)',
                'rgba(54, 162, 235, 0.6)',
                'rgba(255, 205, 86, 0.6)',
                'rgba(75, 192, 192, 0.6)',
                'rgba(153, 102, 255, 0.6)',
                'rgba(255, 159, 64, 0.6)',
                'rgba(199, 199, 199, 0.6)',
                'rgba(83, 102, 255, 0.6)',
                'rgba(255, 99, 255, 0.6)',
                'rgba(99, 255, 132, 0.6)'
            ];

            const datasets = topMethods.map((method, index) => ({
                label: method || '未知方法',
                data: methodGroups[method].totalCounts,
                backgroundColor: colors[index % colors.length],
                borderColor: colors[index % colors.length].replace('0.6', '1'),
                borderWidth: 1
            }));

            // 获取所有时间标签（取第一个方法的时间标签）
            const allLabels = topMethods.length > 0 ? methodGroups[topMethods[0]].labels : [];

            methodChart = new Chart(methodCtx, {
                type: 'line',
                data: {
                    labels: allLabels,
                    datasets: datasets
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: '时间'
                            }
                        },
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '调用次数'
                            }
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: '方法调用趋势 (Top 10)'
                        },
                        tooltip: {
                            callbacks: {
                                afterBody: function(context) {
                                    const dataIndex = context[0].dataIndex;
                                    const method = context[0].dataset.label;
                                    if (methodGroups[method] && methodGroups[method].avgResponseTimes[dataIndex]) {
                                        return '平均响应时间: ' + methodGroups[method].avgResponseTimes[dataIndex] + 'ms';
                                    }
                                    return '';
                                }
                            }
                        }
                    }
                }
            });
        }

        // 加载方法详细分析图表（按秒级统计）
        async function loadMethodDetailChart() {
            const startTime = document.getElementById('startTime').value;
            const endTime = document.getElementById('endTime').value;
            const methodName = document.getElementById('methodName').value;
            const machineName = document.getElementById('machineName').value;

            if (!startTime || !endTime) {
                alert('请选择时间范围');
                return;
            }

            if (!methodName) {
                alert('请选择要分析的方法');
                return;
            }

            try {
                const methodDetailParams = new URLSearchParams({
                    startTime: startTime.replace('T', ' ') + ':00',
                    endTime: endTime.replace('T', ' ') + ':00',
                    methodName: methodName
                });

                if (machineName) {
                    methodDetailParams.append('machineName', machineName);
                }

                const methodDetailResponse = await fetch('/log/statistics/method/second?' + methodDetailParams);
                const methodDetailData = await methodDetailResponse.json();

                if (methodDetailData.length === 0) {
                    alert('选定时间范围内没有该方法的调用数据');
                    return;
                }

                // 显示图表容器
                document.getElementById('methodDetailContainer').style.display = 'block';

                const methodDetailCtx = document.getElementById('methodDetailChart').getContext('2d');

                if (methodDetailChart) {
                    methodDetailChart.destroy();
                }

                // 准备数据
                const labels = methodDetailData.map(item => {
                    // 格式化时间显示，只显示时:分:秒
                    const time = new Date(item.timeLabel);
                    return time.toLocaleTimeString('zh-CN', { hour12: false });
                });

                const avgResponseTimes = methodDetailData.map(item => Math.round(item.avgResponseTime || 0));
                const totalCounts = methodDetailData.map(item => item.totalCount);

                methodDetailChart = new Chart(methodDetailCtx, {
                    type: 'line',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: '平均响应时间 (ms)',
                            data: avgResponseTimes,
                            backgroundColor: 'rgba(75, 192, 192, 0.2)',
                            borderColor: 'rgba(75, 192, 192, 1)',
                            borderWidth: 2,
                            fill: false,
                            tension: 0.1,
                            pointRadius: 4,
                            pointHoverRadius: 6
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            x: {
                                title: {
                                    display: true,
                                    text: '时间 (秒级)'
                                },
                                ticks: {
                                    maxTicksLimit: 20 // 限制显示的时间点数量，避免过于密集
                                }
                            },
                            y: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: '平均响应时间 (毫秒)'
                                }
                            }
                        },
                        plugins: {
                            title: {
                                display: true,
                                text: '方法 "' + methodName + '" 的响应时间趋势分析'
                            },
                            tooltip: {
                                callbacks: {
                                    title: function(context) {
                                        const index = context[0].dataIndex;
                                        return methodDetailData[index].timeLabel;
                                    },
                                    afterBody: function(context) {
                                        const index = context[0].dataIndex;
                                        const item = methodDetailData[index];
                                        return [
                                            '调用次数: ' + item.totalCount,
                                            '成功次数: ' + (item.successCount || 0),
                                            '错误次数: ' + (item.errorCount || 0),
                                            '最大响应时间: ' + (item.maxResponseTime || 0) + 'ms',
                                            '最小响应时间: ' + (item.minResponseTime || 0) + 'ms',
                                            '错误率: ' + (item.errorRate || 0).toFixed(2) + '%'
                                        ];
                                    }
                                }
                            }
                        },
                        interaction: {
                            intersect: false,
                            mode: 'index'
                        }
                    }
                });

                // 滚动到图表位置
                document.getElementById('methodDetailContainer').scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });

            } catch (error) {
                console.error('加载方法详细分析失败:', error);
                alert('加载方法详细分析失败，请检查网络连接');
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initTimeSelectors();
            loadCharts();
        });
    </script>
</body>
</html>
