<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chart Elements Test</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
</head>
<body>
    <h1>Chart Elements Test</h1>
    
    <div>
        <h2>测试Canvas元素是否存在</h2>
        <canvas id="pathChart" width="400" height="200"></canvas>
    </div>

    <script>
        // 测试pathChart元素是否存在
        function testChartElements() {
            console.log('Testing chart elements...');
            
            const pathChartElement = document.getElementById('pathChart');
            console.log('pathChart element:', pathChartElement);
            
            if (pathChartElement) {
                console.log('✓ pathChart element found');
                try {
                    const ctx = pathChartElement.getContext('2d');
                    console.log('✓ getContext() successful:', ctx);
                    
                    // 创建一个简单的测试图表
                    const testChart = new Chart(ctx, {
                        type: 'bar',
                        data: {
                            labels: ['/api/test1', '/api/test2', '/api/test3'],
                            datasets: [{
                                label: '测试数据',
                                data: [10, 20, 15],
                                backgroundColor: 'rgba(54, 162, 235, 0.6)'
                            }]
                        },
                        options: {
                            responsive: true,
                            indexAxis: 'y',
                            plugins: {
                                title: {
                                    display: true,
                                    text: '测试图表'
                                }
                            }
                        }
                    });
                    console.log('✓ Chart created successfully:', testChart);
                } catch (error) {
                    console.error('✗ Error creating chart:', error);
                }
            } else {
                console.error('✗ pathChart element not found');
            }
        }

        // 页面加载完成后测试
        document.addEventListener('DOMContentLoaded', testChartElements);
    </script>
</body>
</html>
