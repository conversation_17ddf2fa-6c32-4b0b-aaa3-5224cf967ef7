package com.marry.suchaologanalysis;

import com.marry.suchaologanalysis.entity.LogRecord;
import com.marry.suchaologanalysis.entity.LogStatistics;
import com.marry.suchaologanalysis.mapper.LogRecordMapper;
import com.marry.suchaologanalysis.service.LogStatisticsService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 方法详细统计功能测试（按秒级统计）
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class MethodDetailStatisticsTest {

    @Autowired
    private LogRecordMapper logRecordMapper;

    @Autowired
    private LogStatisticsService logStatisticsService;

    @BeforeEach
    void setUp() {
        // 清空表数据
        logRecordMapper.truncateTable();
        
        // 准备测试数据 - 同一个方法在不同秒的调用
        LocalDateTime baseTime = LocalDateTime.of(2025, 7, 3, 14, 30, 0);
        
        // 第1秒：login方法调用2次
        LogRecord record1 = new LogRecord();
        record1.setTimestamp(baseTime);
        record1.setPath("/api/user/login");
        record1.setMethod("login");
        record1.setStatus(1);
        record1.setTime(150L);
        record1.setMachineName("server1");
        record1.setClassName("UserController");
        
        LogRecord record2 = new LogRecord();
        record2.setTimestamp(baseTime);
        record2.setPath("/api/user/login");
        record2.setMethod("login");
        record2.setStatus(1);
        record2.setTime(200L);
        record2.setMachineName("server1");
        record2.setClassName("UserController");
        
        // 第2秒：login方法调用1次（有错误）
        LogRecord record3 = new LogRecord();
        record3.setTimestamp(baseTime.plusSeconds(1));
        record3.setPath("/api/user/login");
        record3.setMethod("login");
        record3.setStatus(0);
        record3.setTime(500L);
        record3.setMachineName("server1");
        record3.setClassName("UserController");
        record3.setErrorMsg("登录失败");
        
        // 第3秒：login方法调用1次
        LogRecord record4 = new LogRecord();
        record4.setTimestamp(baseTime.plusSeconds(2));
        record4.setPath("/api/user/login");
        record4.setMethod("login");
        record4.setStatus(1);
        record4.setTime(100L);
        record4.setMachineName("server2");
        record4.setClassName("UserController");
        
        // 第4秒：其他方法调用（不应该出现在login的统计中）
        LogRecord record5 = new LogRecord();
        record5.setTimestamp(baseTime.plusSeconds(3));
        record5.setPath("/api/order/create");
        record5.setMethod("createOrder");
        record5.setStatus(1);
        record5.setTime(300L);
        record5.setMachineName("server1");
        record5.setClassName("OrderController");
        
        logRecordMapper.batchInsert(Arrays.asList(record1, record2, record3, record4, record5));
    }

    @Test
    void testGetMethodStatisticsBySecond() {
        LocalDateTime startTime = LocalDateTime.of(2025, 7, 3, 14, 29, 0);
        LocalDateTime endTime = LocalDateTime.of(2025, 7, 3, 14, 31, 0);
        
        List<LogStatistics> statistics = logStatisticsService.getMethodStatisticsBySecond(
                startTime, endTime, "login");
        
        assertNotNull(statistics);
        assertEquals(3, statistics.size(), "应该有3个时间点的统计数据");
        
        // 验证第1秒的数据
        LogStatistics firstSecond = statistics.get(0);
        assertEquals("login", firstSecond.getMethodName());
        assertEquals(2L, firstSecond.getTotalCount());
        assertEquals(2L, firstSecond.getSuccessCount());
        assertEquals(0L, firstSecond.getErrorCount());
        assertEquals(175.0, firstSecond.getAvgResponseTime(), 0.1); // (150+200)/2
        assertEquals(200L, firstSecond.getMaxResponseTime());
        assertEquals(150L, firstSecond.getMinResponseTime());
        assertEquals(0.0, firstSecond.getErrorRate(), 0.1);
        
        // 验证第2秒的数据（有错误）
        LogStatistics secondSecond = statistics.get(1);
        assertEquals("login", secondSecond.getMethodName());
        assertEquals(1L, secondSecond.getTotalCount());
        assertEquals(0L, secondSecond.getSuccessCount());
        assertEquals(1L, secondSecond.getErrorCount());
        assertEquals(500.0, secondSecond.getAvgResponseTime(), 0.1);
        assertEquals(100.0, secondSecond.getErrorRate(), 0.1); // 1/1 * 100
        
        // 验证第3秒的数据
        LogStatistics thirdSecond = statistics.get(2);
        assertEquals("login", thirdSecond.getMethodName());
        assertEquals(1L, thirdSecond.getTotalCount());
        assertEquals(1L, thirdSecond.getSuccessCount());
        assertEquals(0L, thirdSecond.getErrorCount());
        assertEquals(100.0, thirdSecond.getAvgResponseTime(), 0.1);
    }

    @Test
    void testGetMethodStatisticsBySecondAndMachine() {
        LocalDateTime startTime = LocalDateTime.of(2025, 7, 3, 14, 29, 0);
        LocalDateTime endTime = LocalDateTime.of(2025, 7, 3, 14, 31, 0);
        
        List<LogStatistics> statistics = logStatisticsService.getMethodStatisticsBySecondAndMachine(
                startTime, endTime, "login", "server1");
        
        assertNotNull(statistics);
        assertEquals(2, statistics.size(), "server1应该有2个时间点的login调用");
        
        // 验证第1秒的数据（server1有2次调用）
        LogStatistics firstSecond = statistics.get(0);
        assertEquals(2L, firstSecond.getTotalCount());
        assertEquals(175.0, firstSecond.getAvgResponseTime(), 0.1);
        
        // 验证第2秒的数据（server1有1次调用，有错误）
        LogStatistics secondSecond = statistics.get(1);
        assertEquals(1L, secondSecond.getTotalCount());
        assertEquals(1L, secondSecond.getErrorCount());
        assertEquals(500.0, secondSecond.getAvgResponseTime(), 0.1);
    }

    @Test
    void testGetMethodStatisticsBySecondWithNonExistentMethod() {
        LocalDateTime startTime = LocalDateTime.of(2025, 7, 3, 14, 29, 0);
        LocalDateTime endTime = LocalDateTime.of(2025, 7, 3, 14, 31, 0);
        
        List<LogStatistics> statistics = logStatisticsService.getMethodStatisticsBySecond(
                startTime, endTime, "nonExistentMethod");
        
        assertNotNull(statistics);
        assertTrue(statistics.isEmpty(), "不存在的方法应该返回空列表");
    }

    @Test
    void testGetMethodStatisticsBySecondWithEmptyTimeRange() {
        LocalDateTime startTime = LocalDateTime.of(2025, 7, 3, 15, 0, 0);
        LocalDateTime endTime = LocalDateTime.of(2025, 7, 3, 15, 1, 0);
        
        List<LogStatistics> statistics = logStatisticsService.getMethodStatisticsBySecond(
                startTime, endTime, "login");
        
        assertNotNull(statistics);
        assertTrue(statistics.isEmpty(), "空时间范围应该返回空列表");
    }

    @Test
    void testMethodStatisticsTimeLabels() {
        LocalDateTime startTime = LocalDateTime.of(2025, 7, 3, 14, 29, 0);
        LocalDateTime endTime = LocalDateTime.of(2025, 7, 3, 14, 31, 0);
        
        List<LogStatistics> statistics = logStatisticsService.getMethodStatisticsBySecond(
                startTime, endTime, "login");
        
        assertNotNull(statistics);
        assertFalse(statistics.isEmpty());
        
        // 验证时间标签格式
        for (LogStatistics stat : statistics) {
            assertNotNull(stat.getTimeLabel());
            assertTrue(stat.getTimeLabel().matches("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}"),
                    "时间标签格式应该是 yyyy-MM-dd HH:mm:ss");
        }
        
        // 验证时间顺序（应该按时间升序排列）
        for (int i = 1; i < statistics.size(); i++) {
            String prevTime = statistics.get(i-1).getTimeLabel();
            String currTime = statistics.get(i).getTimeLabel();
            assertTrue(prevTime.compareTo(currTime) <= 0, 
                    "时间应该按升序排列: " + prevTime + " <= " + currTime);
        }
    }
}
