<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日志导入 - 日志分析系统</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #eee;
        }
        .nav-links {
            text-align: center;
            margin-bottom: 30px;
        }
        .nav-links a {
            display: inline-block;
            margin: 0 10px;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            transition: background 0.3s;
        }
        .nav-links a:hover {
            background: #0056b3;
        }
        .upload-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 2px dashed #ddd;
            border-radius: 8px;
            text-align: center;
        }
        .upload-section h3 {
            margin-top: 0;
            color: #333;
        }
        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        .form-group input, .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        .file-input-wrapper {
            position: relative;
            display: inline-block;
            width: 100%;
        }
        .file-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: #f9f9f9;
        }
        .upload-btn {
            background: #28a745;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.3s;
        }
        .upload-btn:hover {
            background: #1e7e34;
        }
        .upload-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .progress {
            width: 100%;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
            display: none;
        }
        .progress-bar {
            height: 100%;
            background: #007bff;
            width: 0%;
            transition: width 0.3s;
        }
        .message {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            display: none;
        }
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .local-import-section {
            margin-top: 40px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        .local-import-section h3 {
            margin-top: 0;
            color: #333;
        }
        .file-mapping {
            margin-bottom: 15px;
            padding: 15px;
            background: white;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
        .file-mapping-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .remove-mapping {
            background: #dc3545;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }
        .add-mapping {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-bottom: 20px;
        }
        .import-local-btn {
            background: #17a2b8;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>日志导入</h1>
            <p>上传日志文件进行分析</p>
        </div>

        <div class="nav-links">
            <a href="/log/">返回首页</a>
        </div>

        <!-- 文件上传区域 -->
        <div class="upload-section">
            <h3>上传日志文件</h3>
            <form id="uploadForm" enctype="multipart/form-data">
                <div class="form-group">
                    <label for="machineNameInput">机器名称</label>
                    <input type="text" id="machineNameInput" name="machineName" placeholder="请输入机器名称，如：server-01" required>
                </div>

                <div class="form-group">
                    <label for="fileInput">选择日志文件</label>
                    <input type="file" id="fileInput" name="file" class="file-input" accept=".log,.txt" required>
                </div>

                <button type="submit" class="upload-btn" id="uploadBtn">上传并导入</button>
            </form>

            <div class="progress" id="progressBar">
                <div class="progress-bar" id="progressBarFill"></div>
            </div>

            <div class="message" id="message"></div>
        </div>

        <!-- 本地文件批量导入区域 -->
        <div class="local-import-section">
            <h3>批量导入本地文件</h3>
            <p>如果您的日志文件已经在服务器上，可以通过指定文件路径进行批量导入</p>

            <div id="fileMappings">
                <div class="file-mapping">
                    <div class="file-mapping-header">
                        <strong>文件映射 1</strong>
                        <button type="button" class="remove-mapping" onclick="removeMapping(this)">删除</button>
                    </div>
                    <div class="form-group">
                        <label>文件路径</label>
                        <input type="text" class="file-path" placeholder="/path/to/logfile.log">
                    </div>
                    <div class="form-group">
                        <label>机器名称</label>
                        <input type="text" class="machine-name" placeholder="server-01">
                    </div>
                </div>
            </div>

            <button type="button" class="add-mapping" onclick="addMapping()">添加文件映射</button>
            <button type="button" class="import-local-btn" onclick="importLocalFiles()">批量导入</button>

            <div class="message" id="localMessage"></div>
        </div>
    </div>

    <script>
        let mappingCount = 1;

        // 文件上传处理
        document.getElementById('uploadForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData();
            const fileInput = document.getElementById('fileInput');
            const machineNameInput = document.getElementById('machineNameInput');

            if (!fileInput.files[0]) {
                showMessage('请选择文件', 'error');
                return;
            }

            if (!machineNameInput.value.trim()) {
                showMessage('请输入机器名称', 'error');
                return;
            }

            formData.append('file', fileInput.files[0]);
            formData.append('machineName', machineNameInput.value.trim());

            const uploadBtn = document.getElementById('uploadBtn');
            const progressBar = document.getElementById('progressBar');
            const progressBarFill = document.getElementById('progressBarFill');

            uploadBtn.disabled = true;
            uploadBtn.textContent = '上传中...';
            progressBar.style.display = 'block';

            try {
                const response = await fetch('/log/upload', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    showMessage(result.message, 'success');
                    document.getElementById('uploadForm').reset();
                } else {
                    showMessage(result.message, 'error');
                }
            } catch (error) {
                showMessage('上传失败: ' + error.message, 'error');
            } finally {
                uploadBtn.disabled = false;
                uploadBtn.textContent = '上传并导入';
                progressBar.style.display = 'none';
                progressBarFill.style.width = '0%';
            }
        });

        // 显示消息
        function showMessage(text, type) {
            const message = document.getElementById('message');
            message.textContent = text;
            message.className = 'message ' + type;
            message.style.display = 'block';

            setTimeout(() => {
                message.style.display = 'none';
            }, 5000);
        }

        // 显示本地导入消息
        function showLocalMessage(text, type) {
            const message = document.getElementById('localMessage');
            message.textContent = text;
            message.className = 'message ' + type;
            message.style.display = 'block';

            setTimeout(() => {
                message.style.display = 'none';
            }, 5000);
        }

        // 添加文件映射
        function addMapping() {
            mappingCount++;
            const mappingsContainer = document.getElementById('fileMappings');

            const mappingDiv = document.createElement('div');
            mappingDiv.className = 'file-mapping';
            mappingDiv.innerHTML =
                '<div class="file-mapping-header">' +
                    '<strong>文件映射 ' + mappingCount + '</strong>' +
                    '<button type="button" class="remove-mapping" onclick="removeMapping(this)">删除</button>' +
                '</div>' +
                '<div class="form-group">' +
                    '<label>文件路径</label>' +
                    '<input type="text" class="file-path" placeholder="/path/to/logfile.log">' +
                '</div>' +
                '<div class="form-group">' +
                    '<label>机器名称</label>' +
                    '<input type="text" class="machine-name" placeholder="server-01">' +
                '</div>';

            mappingsContainer.appendChild(mappingDiv);
        }

        // 删除文件映射
        function removeMapping(button) {
            const mappingsContainer = document.getElementById('fileMappings');
            if (mappingsContainer.children.length > 1) {
                button.closest('.file-mapping').remove();
            } else {
                showLocalMessage('至少需要保留一个文件映射', 'error');
            }
        }

        // 批量导入本地文件
        async function importLocalFiles() {
            const mappings = document.querySelectorAll('.file-mapping');
            const fileInfos = [];

            for (let mapping of mappings) {
                const filePath = mapping.querySelector('.file-path').value.trim();
                const machineName = mapping.querySelector('.machine-name').value.trim();

                if (!filePath || !machineName) {
                    showLocalMessage('请填写完整的文件路径和机器名称', 'error');
                    return;
                }

                fileInfos.push({
                    filePath: filePath,
                    machineName: machineName
                });
            }

            try {
                const response = await fetch('/log/import-local', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(fileInfos)
                });

                const result = await response.json();

                if (result.success) {
                    showLocalMessage(result.message, 'success');
                } else {
                    showLocalMessage(result.message, 'error');
                }
            } catch (error) {
                showLocalMessage('导入失败: ' + error.message, 'error');
            }
        }
    </script>
</body>
</html>
