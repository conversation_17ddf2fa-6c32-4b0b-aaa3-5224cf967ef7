package com.marry.suchaologanalysis.dto;

import lombok.Data;

/**
 * 票务统计数据传输对象
 */
@Data
public class TicketStatisticsDto {
    
    /**
     * 区域统计
     */
    @Data
    public static class RegionStatistics {
        private String region;          // 区域名称
        private Long networkTickets;    // 网络票数量
        private Long groupTickets;      // 团体票数量
        private Long totalTickets;      // 总票数
        
        public RegionStatistics() {}
        
        public RegionStatistics(String region, Long networkTickets, Long groupTickets) {
            this.region = region;
            this.networkTickets = networkTickets;
            this.groupTickets = groupTickets;
            this.totalTickets = networkTickets + groupTickets;
        }
    }
    
    /**
     * 城市统计
     */
    @Data
    public static class CityStatistics {
        private String province;        // 省份
        private String city;           // 城市
        private Long ticketCount;      // 票数量
        
        public CityStatistics() {}
        
        public CityStatistics(String province, String city, Long ticketCount) {
            this.province = province;
            this.city = city;
            this.ticketCount = ticketCount;
        }
    }
    
    /**
     * 年龄统计
     */
    @Data
    public static class AgeStatistics {
        private String ageGroup;       // 年龄组
        private Long ticketCount;      // 票数量
        
        public AgeStatistics() {}
        
        public AgeStatistics(String ageGroup, Long ticketCount) {
            this.ageGroup = ageGroup;
            this.ticketCount = ticketCount;
        }
    }
}
