package com.marry.suchaologanalysis.entity;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 日志记录实体类
 */
@Data
public class LogRecord {
    private Long id;
    private LocalDateTime timestamp;
    private String path;
    private Boolean ajax;
    private Integer status;
    private String errorCode;
    private String errorMsg;
    private Long time;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private String className;
    private String method;
    private String param;
    private String ip;
    private Long netUserId;
    private String performTicketId;
    private String projectId;
    private String performId;
    private Integer num;
    private String psptInfo;
    private Long centerId;
    private Long channelId;
    private LocalDateTime createTime;
}
