<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日志分析系统</title>
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/date-fns@2.29.3/index.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #eee;
        }
        .stats-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-card h3 {
            margin: 0 0 10px 0;
            font-size: 14px;
            opacity: 0.9;
        }
        .stat-card .value {
            font-size: 24px;
            font-weight: bold;
        }
        .controls {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
            align-items: center;
        }
        .control-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        .control-group label {
            font-weight: bold;
            font-size: 12px;
            color: #666;
        }
        input, select, button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            cursor: pointer;
            transition: background 0.3s;
        }
        button:hover {
            background: #0056b3;
        }
        .chart-container {
            margin-bottom: 40px;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .chart-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        .chart-wrapper {
            position: relative;
            height: 400px;
        }
        .nav-links {
            text-align: center;
            margin-bottom: 20px;
        }
        .nav-links a {
            display: inline-block;
            margin: 0 10px;
            padding: 10px 20px;
            background: #28a745;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            transition: background 0.3s;
        }
        .nav-links a:hover {
            background: #1e7e34;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>日志分析系统</h1>
            <p>实时监控和分析系统日志数据</p>
        </div>

        <div class="nav-links">
            <a href="/log/import">导入日志</a>
            <a href="#" onclick="clearLogs()">清空数据</a>
        </div>

        <div class="stats-summary">
            <div class="stat-card">
                <h3>总日志数</h3>
                <div class="value">${totalCount!0}</div>
            </div>
            <div class="stat-card">
                <h3>机器数量</h3>
                <div class="value">${machineNames?size}</div>
            </div>
        </div>

        <div class="controls">
            <div class="control-group">
                <label>开始时间</label>
                <input type="datetime-local" id="startTime" value="">
            </div>
            <div class="control-group">
                <label>结束时间</label>
                <input type="datetime-local" id="endTime" value="">
            </div>
            <div class="control-group">
                <label>机器名称</label>
                <select id="machineName">
                    <option value="">全部机器</option>
                    <#list machineNames as machine>
                        <option value="${machine}">${machine}</option>
                    </#list>
                </select>
            </div>
            <div class="control-group">
                <label>&nbsp;</label>
                <button onclick="loadCharts()">刷新图表</button>
            </div>
        </div>

        <div class="chart-container">
            <div class="chart-title">请求量趋势（按小时）</div>
            <div class="chart-wrapper">
                <canvas id="hourlyChart"></canvas>
            </div>
        </div>

        <div class="chart-container">
            <div class="chart-title">响应时间分布</div>
            <div class="chart-wrapper">
                <canvas id="responseTimeChart"></canvas>
            </div>
        </div>

        <div class="chart-container">
            <div class="chart-title">错误统计</div>
            <div class="chart-wrapper">
                <canvas id="errorChart"></canvas>
            </div>
        </div>
    </div>

    <script>
        let hourlyChart, responseTimeChart, errorChart;

        // 初始化时间选择器
        function initTimeSelectors() {
            const now = new Date();
            const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);

            document.getElementById('startTime').value = formatDateTime(yesterday);
            document.getElementById('endTime').value = formatDateTime(now);
        }

        function formatDateTime(date) {
            return date.toISOString().slice(0, 16);
        }

        // 加载图表数据
        async function loadCharts() {
            const startTime = document.getElementById('startTime').value;
            const endTime = document.getElementById('endTime').value;
            const machineName = document.getElementById('machineName').value;

            if (!startTime || !endTime) {
                alert('请选择时间范围');
                return;
            }

            try {
                await Promise.all([
                    loadHourlyChart(startTime, endTime, machineName),
                    loadResponseTimeChart(startTime, endTime),
                    loadErrorChart(startTime, endTime)
                ]);
            } catch (error) {
                console.error('加载图表失败:', error);
                alert('加载图表失败，请检查网络连接');
            }
        }

        // 加载小时趋势图
        async function loadHourlyChart(startTime, endTime, machineName) {
            const hourlyParams = new URLSearchParams({
                startTime: startTime.replace('T', ' ') + ':00',
                endTime: endTime.replace('T', ' ') + ':00'
            });

            if (machineName) {
                hourlyParams.append('machineName', machineName);
            }

            const hourlyResponse = await fetch('/log/statistics/hourly?' + hourlyParams);
            const hourlyData = await hourlyResponse.json();

            const hourlyCtx = document.getElementById('hourlyChart').getContext('2d');

            if (hourlyChart) {
                hourlyChart.destroy();
            }

            hourlyChart = new Chart(hourlyCtx, {
                type: 'line',
                data: {
                    labels: hourlyData.map(item => item.timeLabel),
                    datasets: [{
                        label: '总请求数',
                        data: hourlyData.map(item => item.totalCount),
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        tension: 0.1
                    }, {
                        label: '成功请求数',
                        data: hourlyData.map(item => item.successCount),
                        borderColor: 'rgb(54, 162, 235)',
                        backgroundColor: 'rgba(54, 162, 235, 0.2)',
                        tension: 0.1
                    }, {
                        label: '错误请求数',
                        data: hourlyData.map(item => item.errorCount),
                        borderColor: 'rgb(255, 99, 132)',
                        backgroundColor: 'rgba(255, 99, 132, 0.2)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        // 加载响应时间分布图
        async function loadResponseTimeChart(startTime, endTime) {
            const responseTimeParams = new URLSearchParams({
                startTime: startTime.replace('T', ' ') + ':00',
                endTime: endTime.replace('T', ' ') + ':00'
            });

            const responseTimeResponse = await fetch('/log/statistics/response-time?' + responseTimeParams);
            const responseTimeData = await responseTimeResponse.json();

            const responseTimeCtx = document.getElementById('responseTimeChart').getContext('2d');

            if (responseTimeChart) {
                responseTimeChart.destroy();
            }

            responseTimeChart = new Chart(responseTimeCtx, {
                type: 'doughnut',
                data: {
                    labels: responseTimeData.map(item => item.timeLabel),
                    datasets: [{
                        data: responseTimeData.map(item => item.totalCount),
                        backgroundColor: [
                            '#FF6384',
                            '#36A2EB',
                            '#FFCE56',
                            '#4BC0C0',
                            '#9966FF'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
        }

        // 加载错误统计图
        async function loadErrorChart(startTime, endTime) {
            const errorParams = new URLSearchParams({
                startTime: startTime.replace('T', ' ') + ':00',
                endTime: endTime.replace('T', ' ') + ':00'
            });

            const errorResponse = await fetch('/log/statistics/errors?' + errorParams);
            const errorData = await errorResponse.json();

            const errorCtx = document.getElementById('errorChart').getContext('2d');

            if (errorChart) {
                errorChart.destroy();
            }

            errorChart = new Chart(errorCtx, {
                type: 'bar',
                data: {
                    labels: errorData.map(item => item.timeLabel || '未知错误'),
                    datasets: [{
                        label: '错误次数',
                        data: errorData.map(item => item.totalCount),
                        backgroundColor: 'rgba(255, 99, 132, 0.8)',
                        borderColor: 'rgba(255, 99, 132, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        // 清空日志
        async function clearLogs() {
            if (!confirm('确定要清空所有日志数据吗？此操作不可恢复！')) {
                return;
            }

            try {
                const response = await fetch('/log/clear', {
                    method: 'POST'
                });
                const result = await response.json();

                if (result.success) {
                    alert('日志数据已清空');
                    location.reload();
                } else {
                    alert('清空失败: ' + result.message);
                }
            } catch (error) {
                console.error('清空失败:', error);
                alert('清空失败，请检查网络连接');
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initTimeSelectors();
            loadCharts();
        });
    </script>
</body>
</html>
