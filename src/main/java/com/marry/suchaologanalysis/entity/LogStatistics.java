package com.marry.suchaologanalysis.entity;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 日志统计数据实体类
 */
@Data
public class LogStatistics {
    private String timeLabel;
    private LocalDateTime timePoint;
    private Long totalCount;
    private Long successCount;
    private Long errorCount;
    private Double avgResponseTime;
    private Long maxResponseTime;
    private Long minResponseTime;
    private Double errorRate;
}
