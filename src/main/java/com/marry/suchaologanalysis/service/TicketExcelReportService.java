package com.marry.suchaologanalysis.service;

import com.marry.suchaologanalysis.dto.TicketStatisticsDto;
import com.marry.suchaologanalysis.mapper.TicketPersonMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;

/**
 * 票务Excel报表生成服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TicketExcelReportService {

    private final TicketPersonMapper ticketPersonMapper;
    private final TicketStatisticsService ticketStatisticsService;

    /**
     * 生成完整的票务统计Excel报表
     */
    public byte[] generateTicketStatisticsReport() throws IOException {
        try (XSSFWorkbook workbook = new XSSFWorkbook()) {
            
            // 创建样式
            CellStyle headerStyle = createHeaderStyle(workbook);
            CellStyle dataStyle = createDataStyle(workbook);
            CellStyle numberStyle = createNumberStyle(workbook);
            
            // 第一个标签页：区域统计
            createRegionStatisticsSheet(workbook, headerStyle, dataStyle, numberStyle);
            
            // 第二个标签页：城市统计
            createCityStatisticsSheet(workbook, headerStyle, dataStyle, numberStyle);
            
            // 第三个标签页：年龄统计
            createAgeStatisticsSheet(workbook, headerStyle, dataStyle, numberStyle);
            
            // 输出到字节数组
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            return outputStream.toByteArray();
            
        } catch (Exception e) {
            log.error("生成Excel报表失败", e);
            throw new IOException("生成Excel报表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建区域统计标签页
     */
    private void createRegionStatisticsSheet(XSSFWorkbook workbook, CellStyle headerStyle, 
                                           CellStyle dataStyle, CellStyle numberStyle) {
        XSSFSheet sheet = workbook.createSheet("区域票务统计");
        
        // 获取区域统计数据
        List<TicketStatisticsDto.RegionStatistics> regionStats = 
            ticketStatisticsService.getRegionStatistics();
        
        // 创建标题
        Row titleRow = sheet.createRow(0);
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellValue("区域票务统计报表");
        titleCell.setCellStyle(headerStyle);
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 3));
        
        // 创建表头
        Row headerRow = sheet.createRow(2);
        String[] headers = {"区域", "网络票数量", "团体票数量", "总票数"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }
        
        // 填充数据
        int rowNum = 3;
        for (TicketStatisticsDto.RegionStatistics stat : regionStats) {
            Row row = sheet.createRow(rowNum++);
            
            Cell regionCell = row.createCell(0);
            regionCell.setCellValue(stat.getRegion());
            regionCell.setCellStyle(dataStyle);
            
            Cell networkCell = row.createCell(1);
            networkCell.setCellValue(stat.getNetworkTickets());
            networkCell.setCellStyle(numberStyle);
            
            Cell groupCell = row.createCell(2);
            groupCell.setCellValue(stat.getGroupTickets());
            groupCell.setCellStyle(numberStyle);
            
            Cell totalCell = row.createCell(3);
            totalCell.setCellValue(stat.getTotalTickets());
            totalCell.setCellStyle(numberStyle);
        }
        
        // 自动调整列宽
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }
        
        // 创建图表（简化版，实际图表需要更复杂的实现）
        createRegionChart(sheet, regionStats, rowNum + 2);
    }

    /**
     * 创建城市统计标签页
     */
    private void createCityStatisticsSheet(XSSFWorkbook workbook, CellStyle headerStyle, 
                                         CellStyle dataStyle, CellStyle numberStyle) {
        XSSFSheet sheet = workbook.createSheet("城市票务统计");
        
        // 获取城市统计数据
        List<TicketStatisticsDto.CityStatistics> cityStats = 
            ticketStatisticsService.getCityStatistics();
        
        // 创建标题
        Row titleRow = sheet.createRow(0);
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellValue("城市票务统计报表");
        titleCell.setCellStyle(headerStyle);
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 2));
        
        // 创建表头
        Row headerRow = sheet.createRow(2);
        String[] headers = {"省份", "城市", "票数量"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }
        
        // 填充数据
        int rowNum = 3;
        for (TicketStatisticsDto.CityStatistics stat : cityStats) {
            Row row = sheet.createRow(rowNum++);
            
            Cell provinceCell = row.createCell(0);
            provinceCell.setCellValue(stat.getProvince() != null ? stat.getProvince() : "");
            provinceCell.setCellStyle(dataStyle);
            
            Cell cityCell = row.createCell(1);
            cityCell.setCellValue(stat.getCity() != null ? stat.getCity() : "");
            cityCell.setCellStyle(dataStyle);
            
            Cell countCell = row.createCell(2);
            countCell.setCellValue(stat.getTicketCount());
            countCell.setCellStyle(numberStyle);
        }
        
        // 自动调整列宽
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }
        
        // 创建图表说明
        createCityChart(sheet, cityStats, rowNum + 2);
    }

    /**
     * 创建年龄统计标签页
     */
    private void createAgeStatisticsSheet(XSSFWorkbook workbook, CellStyle headerStyle, 
                                        CellStyle dataStyle, CellStyle numberStyle) {
        XSSFSheet sheet = workbook.createSheet("年龄票务统计");
        
        // 获取年龄统计数据
        List<TicketStatisticsDto.AgeStatistics> ageStats = 
            ticketStatisticsService.getAgeStatistics();
        
        // 创建标题
        Row titleRow = sheet.createRow(0);
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellValue("年龄票务统计报表");
        titleCell.setCellStyle(headerStyle);
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 1));
        
        // 创建表头
        Row headerRow = sheet.createRow(2);
        String[] headers = {"年龄组", "票数量"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }
        
        // 填充数据
        int rowNum = 3;
        for (TicketStatisticsDto.AgeStatistics stat : ageStats) {
            Row row = sheet.createRow(rowNum++);
            
            Cell ageGroupCell = row.createCell(0);
            ageGroupCell.setCellValue(stat.getAgeGroup());
            ageGroupCell.setCellStyle(dataStyle);
            
            Cell countCell = row.createCell(1);
            countCell.setCellValue(stat.getTicketCount());
            countCell.setCellStyle(numberStyle);
        }
        
        // 自动调整列宽
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }
        
        // 创建图表说明
        createAgeChart(sheet, ageStats, rowNum + 2);
    }

    /**
     * 创建表头样式
     */
    private CellStyle createHeaderStyle(XSSFWorkbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 12);
        style.setFont(font);
        
        return style;
    }

    /**
     * 创建数据样式
     */
    private CellStyle createDataStyle(XSSFWorkbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setAlignment(HorizontalAlignment.LEFT);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        
        return style;
    }

    /**
     * 创建数字样式
     */
    private CellStyle createNumberStyle(XSSFWorkbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setAlignment(HorizontalAlignment.RIGHT);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        
        DataFormat format = workbook.createDataFormat();
        style.setDataFormat(format.getFormat("#,##0"));
        
        return style;
    }

    /**
     * 创建区域图表说明
     */
    private void createRegionChart(XSSFSheet sheet, List<TicketStatisticsDto.RegionStatistics> stats, int startRow) {
        Row chartTitleRow = sheet.createRow(startRow);
        Cell chartTitleCell = chartTitleRow.createCell(0);
        chartTitleCell.setCellValue("图表说明：");
        
        Row chartDescRow = sheet.createRow(startRow + 1);
        Cell chartDescCell = chartDescRow.createCell(0);
        chartDescCell.setCellValue("建议使用Excel的插入图表功能，选择柱状图或条形图来展示区域票务分布");
        
        Row chartDataRow = sheet.createRow(startRow + 2);
        Cell chartDataCell = chartDataRow.createCell(0);
        chartDataCell.setCellValue("数据范围：A3:" + getColumnLetter(3) + (3 + stats.size() - 1));
    }

    /**
     * 创建城市图表说明
     */
    private void createCityChart(XSSFSheet sheet, List<TicketStatisticsDto.CityStatistics> stats, int startRow) {
        Row chartTitleRow = sheet.createRow(startRow);
        Cell chartTitleCell = chartTitleRow.createCell(0);
        chartTitleCell.setCellValue("图表说明：");
        
        Row chartDescRow = sheet.createRow(startRow + 1);
        Cell chartDescCell = chartDescRow.createCell(0);
        chartDescCell.setCellValue("建议使用Excel的插入图表功能，选择柱状图来展示城市票务分布");
        
        Row chartDataRow = sheet.createRow(startRow + 2);
        Cell chartDataCell = chartDataRow.createCell(0);
        chartDataCell.setCellValue("数据范围：A3:" + getColumnLetter(2) + (3 + stats.size() - 1));
    }

    /**
     * 创建年龄图表说明
     */
    private void createAgeChart(XSSFSheet sheet, List<TicketStatisticsDto.AgeStatistics> stats, int startRow) {
        Row chartTitleRow = sheet.createRow(startRow);
        Cell chartTitleCell = chartTitleRow.createCell(0);
        chartTitleCell.setCellValue("图表说明：");
        
        Row chartDescRow = sheet.createRow(startRow + 1);
        Cell chartDescCell = chartDescRow.createCell(0);
        chartDescCell.setCellValue("建议使用Excel的插入图表功能，选择直方图或柱状图来展示年龄分布");
        
        Row chartDataRow = sheet.createRow(startRow + 2);
        Cell chartDataCell = chartDataRow.createCell(0);
        chartDataCell.setCellValue("数据范围：A3:" + getColumnLetter(1) + (3 + stats.size() - 1));
    }

    /**
     * 获取列字母
     */
    private String getColumnLetter(int columnIndex) {
        StringBuilder result = new StringBuilder();
        while (columnIndex >= 0) {
            result.insert(0, (char) ('A' + columnIndex % 26));
            columnIndex = columnIndex / 26 - 1;
        }
        return result.toString();
    }
}
