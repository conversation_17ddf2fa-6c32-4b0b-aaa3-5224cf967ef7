package com.marry.suchaologanalysis.util;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 身份证工具类测试
 */
class IdCardUtilTest {

    @Test
    void testParseValidIdCard() {
        // 测试正常的身份证号码
        String idCard = "32110219900406001X";
        IdCardUtil.IdCardInfo info = IdCardUtil.parseIdCard(idCard);
        
        assertTrue(info.isValid());
        assertEquals("江苏省", info.getProvince());
        assertEquals("镇江市", info.getCity());
        assertEquals("男", info.getGender());
        assertTrue(info.getAge() > 0);
        
        System.out.println("正常身份证解析结果:");
        System.out.println("省份: " + info.getProvince());
        System.out.println("城市: " + info.getCity());
        System.out.println("年龄: " + info.getAge());
        System.out.println("性别: " + info.getGender());
        System.out.println("出生日期: " + info.getBirthDate());
    }

    @Test
    void testParseFutureIdCard() {
        // 测试未来日期的身份证号码
        String idCard = "321102202504060015";
        IdCardUtil.IdCardInfo info = IdCardUtil.parseIdCard(idCard);
        
        System.out.println("未来日期身份证解析结果:");
        System.out.println("是否有效: " + info.isValid());
        System.out.println("省份: " + info.getProvince());
        System.out.println("城市: " + info.getCity());
        System.out.println("年龄: " + info.getAge());
        System.out.println("性别: " + info.getGender());
        System.out.println("出生日期: " + info.getBirthDate());
        System.out.println("错误信息: " + info.getErrorMessage());
        
        if (info.isValid() && info.getAge() != null) {
            System.out.println("计算出的年龄: " + info.getAge());
        }
    }

    @Test
    void testParseInvalidIdCard() {
        // 测试无效的身份证号码
        String idCard = "123456789012345678";
        IdCardUtil.IdCardInfo info = IdCardUtil.parseIdCard(idCard);
        
        assertFalse(info.isValid());
        assertNotNull(info.getErrorMessage());
        
        System.out.println("无效身份证解析结果:");
        System.out.println("是否有效: " + info.isValid());
        System.out.println("错误信息: " + info.getErrorMessage());
    }

    @Test
    void testParse15DigitIdCard() {
        // 测试15位身份证号码
        String idCard = "321102900406001";
        IdCardUtil.IdCardInfo info = IdCardUtil.parseIdCard(idCard);
        
        System.out.println("15位身份证解析结果:");
        System.out.println("是否有效: " + info.isValid());
        System.out.println("省份: " + info.getProvince());
        System.out.println("城市: " + info.getCity());
        System.out.println("年龄: " + info.getAge());
        System.out.println("性别: " + info.getGender());
        System.out.println("出生日期: " + info.getBirthDate());
        System.out.println("错误信息: " + info.getErrorMessage());
    }
}
