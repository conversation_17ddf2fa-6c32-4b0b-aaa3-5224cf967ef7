<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>模板语法测试</title>
</head>
<body>
    <h1>FreeMarker与JavaScript语法冲突测试</h1>
    
    <!-- FreeMarker语法 - 这些应该被服务器端处理 -->
    <div>总数量: ${totalCount!0}</div>
    <div>机器名称: ${machineName!'未知'}</div>
    
    <script>
        // JavaScript字符串拼接 - 修复后的版本
        console.log('修复后的URL拼接:');
        
        // 使用字符串拼接而不是模板字符串
        const params = new URLSearchParams({
            startTime: '2025-07-01 00:00:00',
            endTime: '2025-07-01 23:59:59'
        });
        
        const url1 = '/log/statistics/hourly?' + params;
        console.log('URL1:', url1);
        
        const url2 = '/log/statistics/response-time?' + params;
        console.log('URL2:', url2);
        
        const url3 = '/log/statistics/errors?' + params;
        console.log('URL3:', url3);
        
        // 动态HTML生成 - 修复后的版本
        const mappingCount = 1;
        const htmlContent = 
            '<div class="file-mapping-header">' +
                '<strong>文件映射 ' + mappingCount + '</strong>' +
                '<button type="button" class="remove-mapping">删除</button>' +
            '</div>' +
            '<div class="form-group">' +
                '<label>文件路径</label>' +
                '<input type="text" class="file-path" placeholder="/path/to/logfile.log">' +
            '</div>';
        
        console.log('生成的HTML:', htmlContent);
        
        // 测试FreeMarker变量不会与JavaScript冲突
        console.log('这里不会有FreeMarker语法冲突');
    </script>
</body>
</html>
