package com.marry.suchaologanalysis;

import com.marry.suchaologanalysis.entity.LogStatistics;
import com.marry.suchaologanalysis.service.LogStatisticsService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.time.LocalDateTime;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 路径统计功能测试
 */
@SpringBootTest
@ActiveProfiles("test")
public class PathStatisticsTest {

    @Autowired
    private LogStatisticsService logStatisticsService;

    @Test
    public void testGetPathStatistics() {
        // 测试获取路径统计
        LocalDateTime startTime = LocalDateTime.of(2025, 7, 1, 0, 0);
        LocalDateTime endTime = LocalDateTime.of(2025, 7, 2, 0, 0);
        
        List<LogStatistics> pathStats = logStatisticsService.getPathStatistics(startTime, endTime);
        
        assertNotNull(pathStats);
        // 由于测试环境可能没有数据，这里只验证方法能正常调用
        System.out.println("路径统计结果数量: " + pathStats.size());
        
        for (LogStatistics stat : pathStats) {
            System.out.println("路径: " + stat.getTimeLabel() + 
                             ", 总数: " + stat.getTotalCount() + 
                             ", 成功数: " + stat.getSuccessCount() + 
                             ", 错误数: " + stat.getErrorCount() + 
                             ", 平均响应时间: " + stat.getAvgResponseTime());
        }
    }

    @Test
    public void testGetPathStatisticsByMachine() {
        // 测试按机器获取路径统计
        LocalDateTime startTime = LocalDateTime.of(2025, 7, 1, 0, 0);
        LocalDateTime endTime = LocalDateTime.of(2025, 7, 2, 0, 0);
        String machineName = "test-machine";
        
        List<LogStatistics> pathStats = logStatisticsService.getPathStatisticsByMachine(startTime, endTime, machineName);
        
        assertNotNull(pathStats);
        System.out.println("机器 " + machineName + " 的路径统计结果数量: " + pathStats.size());
    }

    @Test
    public void testGetAllPaths() {
        // 测试获取所有路径列表
        List<String> paths = logStatisticsService.getAllPaths();
        
        assertNotNull(paths);
        System.out.println("所有路径数量: " + paths.size());
        
        for (String path : paths) {
            System.out.println("路径: " + path);
        }
    }
}
