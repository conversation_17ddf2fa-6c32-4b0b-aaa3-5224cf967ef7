package com.marry.suchaologanalysis.util;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * 身份证号码解析工具类
 */
@Slf4j
public class IdCardUtil {

    // 身份证号码正则表达式（18位）
    private static final Pattern ID_CARD_PATTERN = Pattern.compile("^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$");
    
    // 15位身份证号码正则表达式
    private static final Pattern ID_CARD_15_PATTERN = Pattern.compile("^[1-9]\\d{5}\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}$");

    // 地区代码映射表（前6位）
    private static final Map<String, RegionInfo> REGION_CODE_MAP = new HashMap<>();

    static {
        initRegionCodeMap();
    }

    /**
     * 身份证信息解析结果
     */
    @Data
    public static class IdCardInfo {
        private boolean valid;          // 是否有效
        private String province;        // 省份
        private String city;           // 城市
        private Integer age;           // 年龄
        private String gender;         // 性别
        private LocalDate birthDate;   // 出生日期
        private String errorMessage;   // 错误信息
    }

    /**
     * 地区信息
     */
    @Data
    private static class RegionInfo {
        private String province;
        private String city;

        public RegionInfo(String province, String city) {
            this.province = province;
            this.city = city;
        }
    }

    /**
     * 解析身份证号码
     */
    public static IdCardInfo parseIdCard(String idCard) {
        IdCardInfo info = new IdCardInfo();
        
        if (idCard == null || idCard.trim().isEmpty()) {
            info.setValid(false);
            info.setErrorMessage("身份证号码不能为空");
            return info;
        }

        idCard = idCard.trim().toUpperCase();

        // 验证格式
        if (!isValidFormat(idCard)) {
            info.setValid(false);
            info.setErrorMessage("身份证号码格式不正确");
            return info;
        }

        try {
            // 如果是15位，转换为18位
            if (idCard.length() == 15) {
                idCard = convert15To18(idCard);
            }

            // 解析地区信息
            String regionCode = idCard.substring(0, 6);
            RegionInfo regionInfo = getRegionInfo(regionCode);
            if (regionInfo != null) {
                info.setProvince(regionInfo.getProvince());
                info.setCity(regionInfo.getCity());
            }

            // 解析出生日期和年龄
            String birthStr = idCard.substring(6, 14);
            LocalDate birthDate = LocalDate.parse(birthStr, DateTimeFormatter.ofPattern("yyyyMMdd"));
            info.setBirthDate(birthDate);
            
            // 计算年龄
            int age = LocalDate.now().getYear() - birthDate.getYear();
            if (LocalDate.now().getDayOfYear() < birthDate.getDayOfYear()) {
                age--;
            }
            info.setAge(age);

            // 解析性别
            int genderCode = Integer.parseInt(idCard.substring(16, 17));
            info.setGender(genderCode % 2 == 0 ? "女" : "男");

            info.setValid(true);
            
        } catch (Exception e) {
            log.warn("解析身份证号码失败: {}, 错误: {}", idCard, e.getMessage());
            info.setValid(false);
            info.setErrorMessage("身份证号码解析失败: " + e.getMessage());
        }

        return info;
    }

    /**
     * 验证身份证号码格式
     */
    private static boolean isValidFormat(String idCard) {
        if (idCard.length() == 18) {
            return ID_CARD_PATTERN.matcher(idCard).matches();
        } else if (idCard.length() == 15) {
            return ID_CARD_15_PATTERN.matcher(idCard).matches();
        }
        return false;
    }

    /**
     * 15位身份证转18位
     */
    private static String convert15To18(String idCard15) {
        if (idCard15.length() != 15) {
            return idCard15;
        }
        
        String idCard17 = idCard15.substring(0, 6) + "19" + idCard15.substring(6);
        String checkCode = getCheckCode(idCard17);
        return idCard17 + checkCode;
    }

    /**
     * 计算校验码
     */
    private static String getCheckCode(String idCard17) {
        int[] factor = {7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2};
        String[] checkCodes = {"1", "0", "X", "9", "8", "7", "6", "5", "4", "3", "2"};
        
        int sum = 0;
        for (int i = 0; i < 17; i++) {
            sum += Integer.parseInt(String.valueOf(idCard17.charAt(i))) * factor[i];
        }
        
        return checkCodes[sum % 11];
    }

    /**
     * 根据地区代码获取地区信息
     */
    private static RegionInfo getRegionInfo(String regionCode) {
        // 先尝试完整匹配
        RegionInfo info = REGION_CODE_MAP.get(regionCode);
        if (info != null) {
            return info;
        }
        
        // 尝试匹配省级代码（前2位）
        String provinceCode = regionCode.substring(0, 2) + "0000";
        info = REGION_CODE_MAP.get(provinceCode);
        if (info != null) {
            // 尝试匹配市级代码（前4位）
            String cityCode = regionCode.substring(0, 4) + "00";
            RegionInfo cityInfo = REGION_CODE_MAP.get(cityCode);
            if (cityInfo != null) {
                return cityInfo;
            }
            return info;
        }
        
        return null;
    }

    /**
     * 初始化地区代码映射表
     */
    private static void initRegionCodeMap() {
        // 省级行政区
        REGION_CODE_MAP.put("110000", new RegionInfo("北京市", "北京市"));
        REGION_CODE_MAP.put("120000", new RegionInfo("天津市", "天津市"));
        REGION_CODE_MAP.put("130000", new RegionInfo("河北省", ""));
        REGION_CODE_MAP.put("140000", new RegionInfo("山西省", ""));
        REGION_CODE_MAP.put("150000", new RegionInfo("内蒙古自治区", ""));
        REGION_CODE_MAP.put("210000", new RegionInfo("辽宁省", ""));
        REGION_CODE_MAP.put("220000", new RegionInfo("吉林省", ""));
        REGION_CODE_MAP.put("230000", new RegionInfo("黑龙江省", ""));
        REGION_CODE_MAP.put("310000", new RegionInfo("上海市", "上海市"));
        REGION_CODE_MAP.put("320000", new RegionInfo("江苏省", ""));
        REGION_CODE_MAP.put("330000", new RegionInfo("浙江省", ""));
        REGION_CODE_MAP.put("340000", new RegionInfo("安徽省", ""));
        REGION_CODE_MAP.put("350000", new RegionInfo("福建省", ""));
        REGION_CODE_MAP.put("360000", new RegionInfo("江西省", ""));
        REGION_CODE_MAP.put("370000", new RegionInfo("山东省", ""));
        REGION_CODE_MAP.put("410000", new RegionInfo("河南省", ""));
        REGION_CODE_MAP.put("420000", new RegionInfo("湖北省", ""));
        REGION_CODE_MAP.put("430000", new RegionInfo("湖南省", ""));
        REGION_CODE_MAP.put("440000", new RegionInfo("广东省", ""));
        REGION_CODE_MAP.put("450000", new RegionInfo("广西壮族自治区", ""));
        REGION_CODE_MAP.put("460000", new RegionInfo("海南省", ""));
        REGION_CODE_MAP.put("500000", new RegionInfo("重庆市", "重庆市"));
        REGION_CODE_MAP.put("510000", new RegionInfo("四川省", ""));
        REGION_CODE_MAP.put("520000", new RegionInfo("贵州省", ""));
        REGION_CODE_MAP.put("530000", new RegionInfo("云南省", ""));
        REGION_CODE_MAP.put("540000", new RegionInfo("西藏自治区", ""));
        REGION_CODE_MAP.put("610000", new RegionInfo("陕西省", ""));
        REGION_CODE_MAP.put("620000", new RegionInfo("甘肃省", ""));
        REGION_CODE_MAP.put("630000", new RegionInfo("青海省", ""));
        REGION_CODE_MAP.put("640000", new RegionInfo("宁夏回族自治区", ""));
        REGION_CODE_MAP.put("650000", new RegionInfo("新疆维吾尔自治区", ""));
        
        // 主要城市代码（示例）
        REGION_CODE_MAP.put("110100", new RegionInfo("北京市", "北京市"));
        REGION_CODE_MAP.put("120100", new RegionInfo("天津市", "天津市"));
        REGION_CODE_MAP.put("130100", new RegionInfo("河北省", "石家庄市"));
        REGION_CODE_MAP.put("130200", new RegionInfo("河北省", "唐山市"));
        REGION_CODE_MAP.put("130300", new RegionInfo("河北省", "秦皇岛市"));
        REGION_CODE_MAP.put("130400", new RegionInfo("河北省", "邯郸市"));
        REGION_CODE_MAP.put("130500", new RegionInfo("河北省", "邢台市"));
        REGION_CODE_MAP.put("130600", new RegionInfo("河北省", "保定市"));
        REGION_CODE_MAP.put("130700", new RegionInfo("河北省", "张家口市"));
        REGION_CODE_MAP.put("130800", new RegionInfo("河北省", "承德市"));
        REGION_CODE_MAP.put("130900", new RegionInfo("河北省", "沧州市"));
        REGION_CODE_MAP.put("131000", new RegionInfo("河北省", "廊坊市"));
        REGION_CODE_MAP.put("131100", new RegionInfo("河北省", "衡水市"));
        
        REGION_CODE_MAP.put("320100", new RegionInfo("江苏省", "南京市"));
        REGION_CODE_MAP.put("320200", new RegionInfo("江苏省", "无锡市"));
        REGION_CODE_MAP.put("320300", new RegionInfo("江苏省", "徐州市"));
        REGION_CODE_MAP.put("320400", new RegionInfo("江苏省", "常州市"));
        REGION_CODE_MAP.put("320500", new RegionInfo("江苏省", "苏州市"));
        REGION_CODE_MAP.put("320600", new RegionInfo("江苏省", "南通市"));
        
        REGION_CODE_MAP.put("330100", new RegionInfo("浙江省", "杭州市"));
        REGION_CODE_MAP.put("330200", new RegionInfo("浙江省", "宁波市"));
        REGION_CODE_MAP.put("330300", new RegionInfo("浙江省", "温州市"));
        
        REGION_CODE_MAP.put("440100", new RegionInfo("广东省", "广州市"));
        REGION_CODE_MAP.put("440300", new RegionInfo("广东省", "深圳市"));
        REGION_CODE_MAP.put("440400", new RegionInfo("广东省", "珠海市"));
        REGION_CODE_MAP.put("440500", new RegionInfo("广东省", "汕头市"));
        REGION_CODE_MAP.put("440600", new RegionInfo("广东省", "佛山市"));
        
        REGION_CODE_MAP.put("510100", new RegionInfo("四川省", "成都市"));
        REGION_CODE_MAP.put("510300", new RegionInfo("四川省", "自贡市"));
        REGION_CODE_MAP.put("510400", new RegionInfo("四川省", "攀枝花市"));
        
        // 可以继续添加更多城市代码...
    }
}
