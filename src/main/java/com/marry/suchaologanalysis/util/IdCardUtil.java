package com.marry.suchaologanalysis.util;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * 身份证号码解析工具类
 */
@Slf4j
public class IdCardUtil {

    // 身份证号码正则表达式（18位）
    private static final Pattern ID_CARD_PATTERN = Pattern.compile("^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$");
    
    // 15位身份证号码正则表达式
    private static final Pattern ID_CARD_15_PATTERN = Pattern.compile("^[1-9]\\d{5}\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}$");

    // 地区代码映射表（前6位）
    private static final Map<String, RegionInfo> REGION_CODE_MAP = new HashMap<>();

    static {
        initRegionCodeMap();
    }

    /**
     * 身份证信息解析结果
     */
    @Data
    public static class IdCardInfo {
        private boolean valid;          // 是否有效
        private String province;        // 省份
        private String city;           // 城市
        private Integer age;           // 年龄
        private String gender;         // 性别
        private LocalDate birthDate;   // 出生日期
        private String errorMessage;   // 错误信息
    }

    /**
     * 地区信息
     */
    @Data
    private static class RegionInfo {
        private String province;
        private String city;

        public RegionInfo(String province, String city) {
            this.province = province;
            this.city = city;
        }
    }

    /**
     * 解析身份证号码
     */
    public static IdCardInfo parseIdCard(String idCard) {
        IdCardInfo info = new IdCardInfo();
        
        if (idCard == null || idCard.trim().isEmpty()) {
            info.setValid(false);
            info.setErrorMessage("身份证号码不能为空");
            return info;
        }

        idCard = idCard.trim().toUpperCase();

        // 验证格式
        if (!isValidFormat(idCard)) {
            info.setValid(false);
            info.setErrorMessage("身份证号码格式不正确");
            return info;
        }

        try {
            // 如果是15位，转换为18位
            if (idCard.length() == 15) {
                idCard = convert15To18(idCard);
            }

            // 解析地区信息
            String regionCode = idCard.substring(0, 6);
            RegionInfo regionInfo = getRegionInfo(regionCode);
            if (regionInfo != null) {
                info.setProvince(regionInfo.getProvince());
                info.setCity(regionInfo.getCity());
            }

            // 解析出生日期和年龄
            String birthStr = idCard.substring(6, 14);
            LocalDate birthDate = LocalDate.parse(birthStr, DateTimeFormatter.ofPattern("yyyyMMdd"));
            info.setBirthDate(birthDate);
            
            // 计算年龄
            int age = LocalDate.now().getYear() - birthDate.getYear();
            if (LocalDate.now().getDayOfYear() < birthDate.getDayOfYear()) {
                age--;
            }
            info.setAge(age);

            // 解析性别
            int genderCode = Integer.parseInt(idCard.substring(16, 17));
            info.setGender(genderCode % 2 == 0 ? "女" : "男");

            info.setValid(true);
            
        } catch (Exception e) {
            log.warn("解析身份证号码失败: {}, 错误: {}", idCard, e.getMessage());
            info.setValid(false);
            info.setErrorMessage("身份证号码解析失败: " + e.getMessage());
        }

        return info;
    }

    /**
     * 验证身份证号码格式
     */
    private static boolean isValidFormat(String idCard) {
        if (idCard.length() == 18) {
            return ID_CARD_PATTERN.matcher(idCard).matches();
        } else if (idCard.length() == 15) {
            return ID_CARD_15_PATTERN.matcher(idCard).matches();
        }
        return false;
    }

    /**
     * 15位身份证转18位
     */
    private static String convert15To18(String idCard15) {
        if (idCard15.length() != 15) {
            return idCard15;
        }
        
        String idCard17 = idCard15.substring(0, 6) + "19" + idCard15.substring(6);
        String checkCode = getCheckCode(idCard17);
        return idCard17 + checkCode;
    }

    /**
     * 计算校验码
     */
    private static String getCheckCode(String idCard17) {
        int[] factor = {7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2};
        String[] checkCodes = {"1", "0", "X", "9", "8", "7", "6", "5", "4", "3", "2"};
        
        int sum = 0;
        for (int i = 0; i < 17; i++) {
            sum += Integer.parseInt(String.valueOf(idCard17.charAt(i))) * factor[i];
        }
        
        return checkCodes[sum % 11];
    }

    /**
     * 根据地区代码获取地区信息
     */
    private static RegionInfo getRegionInfo(String regionCode) {
        // 先尝试完整匹配
        RegionInfo info = REGION_CODE_MAP.get(regionCode);
        if (info != null) {
            return info;
        }
        
        // 尝试匹配省级代码（前2位）
        String provinceCode = regionCode.substring(0, 2) + "0000";
        info = REGION_CODE_MAP.get(provinceCode);
        if (info != null) {
            // 尝试匹配市级代码（前4位）
            String cityCode = regionCode.substring(0, 4) + "00";
            RegionInfo cityInfo = REGION_CODE_MAP.get(cityCode);
            if (cityInfo != null) {
                return cityInfo;
            }
            return info;
        }
        
        return null;
    }

    /**
     * 初始化地区代码映射表
     */
    private static void initRegionCodeMap() {
        // 直辖市
        REGION_CODE_MAP.put("110000", new RegionInfo("北京市", "北京市"));
        REGION_CODE_MAP.put("110100", new RegionInfo("北京市", "北京市"));
        REGION_CODE_MAP.put("120000", new RegionInfo("天津市", "天津市"));
        REGION_CODE_MAP.put("120100", new RegionInfo("天津市", "天津市"));
        REGION_CODE_MAP.put("310000", new RegionInfo("上海市", "上海市"));
        REGION_CODE_MAP.put("310100", new RegionInfo("上海市", "上海市"));
        REGION_CODE_MAP.put("500000", new RegionInfo("重庆市", "重庆市"));
        REGION_CODE_MAP.put("500100", new RegionInfo("重庆市", "重庆市"));

        // 河北省
        REGION_CODE_MAP.put("130000", new RegionInfo("河北省", ""));
        REGION_CODE_MAP.put("130100", new RegionInfo("河北省", "石家庄市"));
        REGION_CODE_MAP.put("130200", new RegionInfo("河北省", "唐山市"));
        REGION_CODE_MAP.put("130300", new RegionInfo("河北省", "秦皇岛市"));
        REGION_CODE_MAP.put("130400", new RegionInfo("河北省", "邯郸市"));
        REGION_CODE_MAP.put("130500", new RegionInfo("河北省", "邢台市"));
        REGION_CODE_MAP.put("130600", new RegionInfo("河北省", "保定市"));
        REGION_CODE_MAP.put("130700", new RegionInfo("河北省", "张家口市"));
        REGION_CODE_MAP.put("130800", new RegionInfo("河北省", "承德市"));
        REGION_CODE_MAP.put("130900", new RegionInfo("河北省", "沧州市"));
        REGION_CODE_MAP.put("131000", new RegionInfo("河北省", "廊坊市"));
        REGION_CODE_MAP.put("131100", new RegionInfo("河北省", "衡水市"));

        // 山西省
        REGION_CODE_MAP.put("140000", new RegionInfo("山西省", ""));
        REGION_CODE_MAP.put("140100", new RegionInfo("山西省", "太原市"));
        REGION_CODE_MAP.put("140200", new RegionInfo("山西省", "大同市"));
        REGION_CODE_MAP.put("140300", new RegionInfo("山西省", "阳泉市"));
        REGION_CODE_MAP.put("140400", new RegionInfo("山西省", "长治市"));
        REGION_CODE_MAP.put("140500", new RegionInfo("山西省", "晋城市"));
        REGION_CODE_MAP.put("140600", new RegionInfo("山西省", "朔州市"));
        REGION_CODE_MAP.put("140700", new RegionInfo("山西省", "晋中市"));
        REGION_CODE_MAP.put("140800", new RegionInfo("山西省", "运城市"));
        REGION_CODE_MAP.put("140900", new RegionInfo("山西省", "忻州市"));
        REGION_CODE_MAP.put("141000", new RegionInfo("山西省", "临汾市"));
        REGION_CODE_MAP.put("141100", new RegionInfo("山西省", "吕梁市"));

        // 内蒙古自治区
        REGION_CODE_MAP.put("150000", new RegionInfo("内蒙古自治区", ""));
        REGION_CODE_MAP.put("150100", new RegionInfo("内蒙古自治区", "呼和浩特市"));
        REGION_CODE_MAP.put("150200", new RegionInfo("内蒙古自治区", "包头市"));
        REGION_CODE_MAP.put("150300", new RegionInfo("内蒙古自治区", "乌海市"));
        REGION_CODE_MAP.put("150400", new RegionInfo("内蒙古自治区", "赤峰市"));
        REGION_CODE_MAP.put("150500", new RegionInfo("内蒙古自治区", "通辽市"));
        REGION_CODE_MAP.put("150600", new RegionInfo("内蒙古自治区", "鄂尔多斯市"));
        REGION_CODE_MAP.put("150700", new RegionInfo("内蒙古自治区", "呼伦贝尔市"));
        REGION_CODE_MAP.put("150800", new RegionInfo("内蒙古自治区", "巴彦淖尔市"));
        REGION_CODE_MAP.put("150900", new RegionInfo("内蒙古自治区", "乌兰察布市"));

        // 辽宁省
        REGION_CODE_MAP.put("210000", new RegionInfo("辽宁省", ""));
        REGION_CODE_MAP.put("210100", new RegionInfo("辽宁省", "沈阳市"));
        REGION_CODE_MAP.put("210200", new RegionInfo("辽宁省", "大连市"));
        REGION_CODE_MAP.put("210300", new RegionInfo("辽宁省", "鞍山市"));
        REGION_CODE_MAP.put("210400", new RegionInfo("辽宁省", "抚顺市"));
        REGION_CODE_MAP.put("210500", new RegionInfo("辽宁省", "本溪市"));
        REGION_CODE_MAP.put("210600", new RegionInfo("辽宁省", "丹东市"));
        REGION_CODE_MAP.put("210700", new RegionInfo("辽宁省", "锦州市"));
        REGION_CODE_MAP.put("210800", new RegionInfo("辽宁省", "营口市"));
        REGION_CODE_MAP.put("210900", new RegionInfo("辽宁省", "阜新市"));
        REGION_CODE_MAP.put("211000", new RegionInfo("辽宁省", "辽阳市"));
        REGION_CODE_MAP.put("211100", new RegionInfo("辽宁省", "盘锦市"));
        REGION_CODE_MAP.put("211200", new RegionInfo("辽宁省", "铁岭市"));
        REGION_CODE_MAP.put("211300", new RegionInfo("辽宁省", "朝阳市"));
        REGION_CODE_MAP.put("211400", new RegionInfo("辽宁省", "葫芦岛市"));

        // 吉林省
        REGION_CODE_MAP.put("220000", new RegionInfo("吉林省", ""));
        REGION_CODE_MAP.put("220100", new RegionInfo("吉林省", "长春市"));
        REGION_CODE_MAP.put("220200", new RegionInfo("吉林省", "吉林市"));
        REGION_CODE_MAP.put("220300", new RegionInfo("吉林省", "四平市"));
        REGION_CODE_MAP.put("220400", new RegionInfo("吉林省", "辽源市"));
        REGION_CODE_MAP.put("220500", new RegionInfo("吉林省", "通化市"));
        REGION_CODE_MAP.put("220600", new RegionInfo("吉林省", "白山市"));
        REGION_CODE_MAP.put("220700", new RegionInfo("吉林省", "松原市"));
        REGION_CODE_MAP.put("220800", new RegionInfo("吉林省", "白城市"));

        // 黑龙江省
        REGION_CODE_MAP.put("230000", new RegionInfo("黑龙江省", ""));
        REGION_CODE_MAP.put("230100", new RegionInfo("黑龙江省", "哈尔滨市"));
        REGION_CODE_MAP.put("230200", new RegionInfo("黑龙江省", "齐齐哈尔市"));
        REGION_CODE_MAP.put("230300", new RegionInfo("黑龙江省", "鸡西市"));
        REGION_CODE_MAP.put("230400", new RegionInfo("黑龙江省", "鹤岗市"));
        REGION_CODE_MAP.put("230500", new RegionInfo("黑龙江省", "双鸭山市"));
        REGION_CODE_MAP.put("230600", new RegionInfo("黑龙江省", "大庆市"));
        REGION_CODE_MAP.put("230700", new RegionInfo("黑龙江省", "伊春市"));
        REGION_CODE_MAP.put("230800", new RegionInfo("黑龙江省", "佳木斯市"));
        REGION_CODE_MAP.put("230900", new RegionInfo("黑龙江省", "七台河市"));
        REGION_CODE_MAP.put("231000", new RegionInfo("黑龙江省", "牡丹江市"));
        REGION_CODE_MAP.put("231100", new RegionInfo("黑龙江省", "黑河市"));
        REGION_CODE_MAP.put("231200", new RegionInfo("黑龙江省", "绥化市"));

        // 江苏省
        REGION_CODE_MAP.put("320000", new RegionInfo("江苏省", ""));
        REGION_CODE_MAP.put("320100", new RegionInfo("江苏省", "南京市"));
        REGION_CODE_MAP.put("320200", new RegionInfo("江苏省", "无锡市"));
        REGION_CODE_MAP.put("320300", new RegionInfo("江苏省", "徐州市"));
        REGION_CODE_MAP.put("320400", new RegionInfo("江苏省", "常州市"));
        REGION_CODE_MAP.put("320500", new RegionInfo("江苏省", "苏州市"));
        REGION_CODE_MAP.put("320600", new RegionInfo("江苏省", "南通市"));
        REGION_CODE_MAP.put("320700", new RegionInfo("江苏省", "连云港市"));
        REGION_CODE_MAP.put("320800", new RegionInfo("江苏省", "淮安市"));
        REGION_CODE_MAP.put("320900", new RegionInfo("江苏省", "盐城市"));
        REGION_CODE_MAP.put("321000", new RegionInfo("江苏省", "扬州市"));
        REGION_CODE_MAP.put("321100", new RegionInfo("江苏省", "镇江市"));
        REGION_CODE_MAP.put("321200", new RegionInfo("江苏省", "泰州市"));
        REGION_CODE_MAP.put("321300", new RegionInfo("江苏省", "宿迁市"));

        // 浙江省
        REGION_CODE_MAP.put("330000", new RegionInfo("浙江省", ""));
        REGION_CODE_MAP.put("330100", new RegionInfo("浙江省", "杭州市"));
        REGION_CODE_MAP.put("330200", new RegionInfo("浙江省", "宁波市"));
        REGION_CODE_MAP.put("330300", new RegionInfo("浙江省", "温州市"));
        REGION_CODE_MAP.put("330400", new RegionInfo("浙江省", "嘉兴市"));
        REGION_CODE_MAP.put("330500", new RegionInfo("浙江省", "湖州市"));
        REGION_CODE_MAP.put("330600", new RegionInfo("浙江省", "绍兴市"));
        REGION_CODE_MAP.put("330700", new RegionInfo("浙江省", "金华市"));
        REGION_CODE_MAP.put("330800", new RegionInfo("浙江省", "衢州市"));
        REGION_CODE_MAP.put("330900", new RegionInfo("浙江省", "舟山市"));
        REGION_CODE_MAP.put("331000", new RegionInfo("浙江省", "台州市"));
        REGION_CODE_MAP.put("331100", new RegionInfo("浙江省", "丽水市"));

        // 安徽省
        REGION_CODE_MAP.put("340000", new RegionInfo("安徽省", ""));
        REGION_CODE_MAP.put("340100", new RegionInfo("安徽省", "合肥市"));
        REGION_CODE_MAP.put("340200", new RegionInfo("安徽省", "芜湖市"));
        REGION_CODE_MAP.put("340300", new RegionInfo("安徽省", "蚌埠市"));
        REGION_CODE_MAP.put("340400", new RegionInfo("安徽省", "淮南市"));
        REGION_CODE_MAP.put("340500", new RegionInfo("安徽省", "马鞍山市"));
        REGION_CODE_MAP.put("340600", new RegionInfo("安徽省", "淮北市"));
        REGION_CODE_MAP.put("340700", new RegionInfo("安徽省", "铜陵市"));
        REGION_CODE_MAP.put("340800", new RegionInfo("安徽省", "安庆市"));
        REGION_CODE_MAP.put("341000", new RegionInfo("安徽省", "黄山市"));
        REGION_CODE_MAP.put("341100", new RegionInfo("安徽省", "滁州市"));
        REGION_CODE_MAP.put("341200", new RegionInfo("安徽省", "阜阳市"));
        REGION_CODE_MAP.put("341300", new RegionInfo("安徽省", "宿州市"));
        REGION_CODE_MAP.put("341500", new RegionInfo("安徽省", "六安市"));
        REGION_CODE_MAP.put("341600", new RegionInfo("安徽省", "亳州市"));
        REGION_CODE_MAP.put("341700", new RegionInfo("安徽省", "池州市"));
        REGION_CODE_MAP.put("341800", new RegionInfo("安徽省", "宣城市"));

        // 福建省
        REGION_CODE_MAP.put("350000", new RegionInfo("福建省", ""));
        REGION_CODE_MAP.put("350100", new RegionInfo("福建省", "福州市"));
        REGION_CODE_MAP.put("350200", new RegionInfo("福建省", "厦门市"));
        REGION_CODE_MAP.put("350300", new RegionInfo("福建省", "莆田市"));
        REGION_CODE_MAP.put("350400", new RegionInfo("福建省", "三明市"));
        REGION_CODE_MAP.put("350500", new RegionInfo("福建省", "泉州市"));
        REGION_CODE_MAP.put("350600", new RegionInfo("福建省", "漳州市"));
        REGION_CODE_MAP.put("350700", new RegionInfo("福建省", "南平市"));
        REGION_CODE_MAP.put("350800", new RegionInfo("福建省", "龙岩市"));
        REGION_CODE_MAP.put("350900", new RegionInfo("福建省", "宁德市"));

        // 江西省
        REGION_CODE_MAP.put("360000", new RegionInfo("江西省", ""));
        REGION_CODE_MAP.put("360100", new RegionInfo("江西省", "南昌市"));
        REGION_CODE_MAP.put("360200", new RegionInfo("江西省", "景德镇市"));
        REGION_CODE_MAP.put("360300", new RegionInfo("江西省", "萍乡市"));
        REGION_CODE_MAP.put("360400", new RegionInfo("江西省", "九江市"));
        REGION_CODE_MAP.put("360500", new RegionInfo("江西省", "新余市"));
        REGION_CODE_MAP.put("360600", new RegionInfo("江西省", "鹰潭市"));
        REGION_CODE_MAP.put("360700", new RegionInfo("江西省", "赣州市"));
        REGION_CODE_MAP.put("360800", new RegionInfo("江西省", "吉安市"));
        REGION_CODE_MAP.put("360900", new RegionInfo("江西省", "宜春市"));
        REGION_CODE_MAP.put("361000", new RegionInfo("江西省", "抚州市"));
        REGION_CODE_MAP.put("361100", new RegionInfo("江西省", "上饶市"));

        // 山东省
        REGION_CODE_MAP.put("370000", new RegionInfo("山东省", ""));
        REGION_CODE_MAP.put("370100", new RegionInfo("山东省", "济南市"));
        REGION_CODE_MAP.put("370200", new RegionInfo("山东省", "青岛市"));
        REGION_CODE_MAP.put("370300", new RegionInfo("山东省", "淄博市"));
        REGION_CODE_MAP.put("370400", new RegionInfo("山东省", "枣庄市"));
        REGION_CODE_MAP.put("370500", new RegionInfo("山东省", "东营市"));
        REGION_CODE_MAP.put("370600", new RegionInfo("山东省", "烟台市"));
        REGION_CODE_MAP.put("370700", new RegionInfo("山东省", "潍坊市"));
        REGION_CODE_MAP.put("370800", new RegionInfo("山东省", "济宁市"));
        REGION_CODE_MAP.put("370900", new RegionInfo("山东省", "泰安市"));
        REGION_CODE_MAP.put("371000", new RegionInfo("山东省", "威海市"));
        REGION_CODE_MAP.put("371100", new RegionInfo("山东省", "日照市"));
        REGION_CODE_MAP.put("371200", new RegionInfo("山东省", "莱芜市"));
        REGION_CODE_MAP.put("371300", new RegionInfo("山东省", "临沂市"));
        REGION_CODE_MAP.put("371400", new RegionInfo("山东省", "德州市"));
        REGION_CODE_MAP.put("371500", new RegionInfo("山东省", "聊城市"));
        REGION_CODE_MAP.put("371600", new RegionInfo("山东省", "滨州市"));
        REGION_CODE_MAP.put("371700", new RegionInfo("山东省", "菏泽市"));

        // 河南省
        REGION_CODE_MAP.put("410000", new RegionInfo("河南省", ""));
        REGION_CODE_MAP.put("410100", new RegionInfo("河南省", "郑州市"));
        REGION_CODE_MAP.put("410200", new RegionInfo("河南省", "开封市"));
        REGION_CODE_MAP.put("410300", new RegionInfo("河南省", "洛阳市"));
        REGION_CODE_MAP.put("410400", new RegionInfo("河南省", "平顶山市"));
        REGION_CODE_MAP.put("410500", new RegionInfo("河南省", "安阳市"));
        REGION_CODE_MAP.put("410600", new RegionInfo("河南省", "鹤壁市"));
        REGION_CODE_MAP.put("410700", new RegionInfo("河南省", "新乡市"));
        REGION_CODE_MAP.put("410800", new RegionInfo("河南省", "焦作市"));
        REGION_CODE_MAP.put("410900", new RegionInfo("河南省", "濮阳市"));
        REGION_CODE_MAP.put("411000", new RegionInfo("河南省", "许昌市"));
        REGION_CODE_MAP.put("411100", new RegionInfo("河南省", "漯河市"));
        REGION_CODE_MAP.put("411200", new RegionInfo("河南省", "三门峡市"));
        REGION_CODE_MAP.put("411300", new RegionInfo("河南省", "南阳市"));
        REGION_CODE_MAP.put("411400", new RegionInfo("河南省", "商丘市"));
        REGION_CODE_MAP.put("411500", new RegionInfo("河南省", "信阳市"));
        REGION_CODE_MAP.put("411600", new RegionInfo("河南省", "周口市"));
        REGION_CODE_MAP.put("411700", new RegionInfo("河南省", "驻马店市"));

        // 湖北省
        REGION_CODE_MAP.put("420000", new RegionInfo("湖北省", ""));
        REGION_CODE_MAP.put("420100", new RegionInfo("湖北省", "武汉市"));
        REGION_CODE_MAP.put("420200", new RegionInfo("湖北省", "黄石市"));
        REGION_CODE_MAP.put("420300", new RegionInfo("湖北省", "十堰市"));
        REGION_CODE_MAP.put("420500", new RegionInfo("湖北省", "宜昌市"));
        REGION_CODE_MAP.put("420600", new RegionInfo("湖北省", "襄阳市"));
        REGION_CODE_MAP.put("420700", new RegionInfo("湖北省", "鄂州市"));
        REGION_CODE_MAP.put("420800", new RegionInfo("湖北省", "荆门市"));
        REGION_CODE_MAP.put("420900", new RegionInfo("湖北省", "孝感市"));
        REGION_CODE_MAP.put("421000", new RegionInfo("湖北省", "荆州市"));
        REGION_CODE_MAP.put("421100", new RegionInfo("湖北省", "黄冈市"));
        REGION_CODE_MAP.put("421200", new RegionInfo("湖北省", "咸宁市"));
        REGION_CODE_MAP.put("421300", new RegionInfo("湖北省", "随州市"));

        // 湖南省
        REGION_CODE_MAP.put("430000", new RegionInfo("湖南省", ""));
        REGION_CODE_MAP.put("430100", new RegionInfo("湖南省", "长沙市"));
        REGION_CODE_MAP.put("430200", new RegionInfo("湖南省", "株洲市"));
        REGION_CODE_MAP.put("430300", new RegionInfo("湖南省", "湘潭市"));
        REGION_CODE_MAP.put("430400", new RegionInfo("湖南省", "衡阳市"));
        REGION_CODE_MAP.put("430500", new RegionInfo("湖南省", "邵阳市"));
        REGION_CODE_MAP.put("430600", new RegionInfo("湖南省", "岳阳市"));
        REGION_CODE_MAP.put("430700", new RegionInfo("湖南省", "常德市"));
        REGION_CODE_MAP.put("430800", new RegionInfo("湖南省", "张家界市"));
        REGION_CODE_MAP.put("430900", new RegionInfo("湖南省", "益阳市"));
        REGION_CODE_MAP.put("431000", new RegionInfo("湖南省", "郴州市"));
        REGION_CODE_MAP.put("431100", new RegionInfo("湖南省", "永州市"));
        REGION_CODE_MAP.put("431200", new RegionInfo("湖南省", "怀化市"));
        REGION_CODE_MAP.put("431300", new RegionInfo("湖南省", "娄底市"));

        // 广东省
        REGION_CODE_MAP.put("440000", new RegionInfo("广东省", ""));
        REGION_CODE_MAP.put("440100", new RegionInfo("广东省", "广州市"));
        REGION_CODE_MAP.put("440200", new RegionInfo("广东省", "韶关市"));
        REGION_CODE_MAP.put("440300", new RegionInfo("广东省", "深圳市"));
        REGION_CODE_MAP.put("440400", new RegionInfo("广东省", "珠海市"));
        REGION_CODE_MAP.put("440500", new RegionInfo("广东省", "汕头市"));
        REGION_CODE_MAP.put("440600", new RegionInfo("广东省", "佛山市"));
        REGION_CODE_MAP.put("440700", new RegionInfo("广东省", "江门市"));
        REGION_CODE_MAP.put("440800", new RegionInfo("广东省", "湛江市"));
        REGION_CODE_MAP.put("440900", new RegionInfo("广东省", "茂名市"));
        REGION_CODE_MAP.put("441200", new RegionInfo("广东省", "肇庆市"));
        REGION_CODE_MAP.put("441300", new RegionInfo("广东省", "惠州市"));
        REGION_CODE_MAP.put("441400", new RegionInfo("广东省", "梅州市"));
        REGION_CODE_MAP.put("441500", new RegionInfo("广东省", "汕尾市"));
        REGION_CODE_MAP.put("441600", new RegionInfo("广东省", "河源市"));
        REGION_CODE_MAP.put("441700", new RegionInfo("广东省", "阳江市"));
        REGION_CODE_MAP.put("441800", new RegionInfo("广东省", "清远市"));
        REGION_CODE_MAP.put("441900", new RegionInfo("广东省", "东莞市"));
        REGION_CODE_MAP.put("442000", new RegionInfo("广东省", "中山市"));
        REGION_CODE_MAP.put("445100", new RegionInfo("广东省", "潮州市"));
        REGION_CODE_MAP.put("445200", new RegionInfo("广东省", "揭阳市"));
        REGION_CODE_MAP.put("445300", new RegionInfo("广东省", "云浮市"));

        // 广西壮族自治区
        REGION_CODE_MAP.put("450000", new RegionInfo("广西壮族自治区", ""));
        REGION_CODE_MAP.put("450100", new RegionInfo("广西壮族自治区", "南宁市"));
        REGION_CODE_MAP.put("450200", new RegionInfo("广西壮族自治区", "柳州市"));
        REGION_CODE_MAP.put("450300", new RegionInfo("广西壮族自治区", "桂林市"));
        REGION_CODE_MAP.put("450400", new RegionInfo("广西壮族自治区", "梧州市"));
        REGION_CODE_MAP.put("450500", new RegionInfo("广西壮族自治区", "北海市"));
        REGION_CODE_MAP.put("450600", new RegionInfo("广西壮族自治区", "防城港市"));
        REGION_CODE_MAP.put("450700", new RegionInfo("广西壮族自治区", "钦州市"));
        REGION_CODE_MAP.put("450800", new RegionInfo("广西壮族自治区", "贵港市"));
        REGION_CODE_MAP.put("450900", new RegionInfo("广西壮族自治区", "玉林市"));
        REGION_CODE_MAP.put("451000", new RegionInfo("广西壮族自治区", "百色市"));
        REGION_CODE_MAP.put("451100", new RegionInfo("广西壮族自治区", "贺州市"));
        REGION_CODE_MAP.put("451200", new RegionInfo("广西壮族自治区", "河池市"));
        REGION_CODE_MAP.put("451300", new RegionInfo("广西壮族自治区", "来宾市"));
        REGION_CODE_MAP.put("451400", new RegionInfo("广西壮族自治区", "崇左市"));

        // 海南省
        REGION_CODE_MAP.put("460000", new RegionInfo("海南省", ""));
        REGION_CODE_MAP.put("460100", new RegionInfo("海南省", "海口市"));
        REGION_CODE_MAP.put("460200", new RegionInfo("海南省", "三亚市"));
        REGION_CODE_MAP.put("460300", new RegionInfo("海南省", "三沙市"));
        REGION_CODE_MAP.put("460400", new RegionInfo("海南省", "儋州市"));

        // 四川省
        REGION_CODE_MAP.put("510000", new RegionInfo("四川省", ""));
        REGION_CODE_MAP.put("510100", new RegionInfo("四川省", "成都市"));
        REGION_CODE_MAP.put("510300", new RegionInfo("四川省", "自贡市"));
        REGION_CODE_MAP.put("510400", new RegionInfo("四川省", "攀枝花市"));
        REGION_CODE_MAP.put("510500", new RegionInfo("四川省", "泸州市"));
        REGION_CODE_MAP.put("510600", new RegionInfo("四川省", "德阳市"));
        REGION_CODE_MAP.put("510700", new RegionInfo("四川省", "绵阳市"));
        REGION_CODE_MAP.put("510800", new RegionInfo("四川省", "广元市"));
        REGION_CODE_MAP.put("510900", new RegionInfo("四川省", "遂宁市"));
        REGION_CODE_MAP.put("511000", new RegionInfo("四川省", "内江市"));
        REGION_CODE_MAP.put("511100", new RegionInfo("四川省", "乐山市"));
        REGION_CODE_MAP.put("511300", new RegionInfo("四川省", "南充市"));
        REGION_CODE_MAP.put("511400", new RegionInfo("四川省", "眉山市"));
        REGION_CODE_MAP.put("511500", new RegionInfo("四川省", "宜宾市"));
        REGION_CODE_MAP.put("511600", new RegionInfo("四川省", "广安市"));
        REGION_CODE_MAP.put("511700", new RegionInfo("四川省", "达州市"));
        REGION_CODE_MAP.put("511800", new RegionInfo("四川省", "雅安市"));
        REGION_CODE_MAP.put("511900", new RegionInfo("四川省", "巴中市"));
        REGION_CODE_MAP.put("512000", new RegionInfo("四川省", "资阳市"));

        // 贵州省
        REGION_CODE_MAP.put("520000", new RegionInfo("贵州省", ""));
        REGION_CODE_MAP.put("520100", new RegionInfo("贵州省", "贵阳市"));
        REGION_CODE_MAP.put("520200", new RegionInfo("贵州省", "六盘水市"));
        REGION_CODE_MAP.put("520300", new RegionInfo("贵州省", "遵义市"));
        REGION_CODE_MAP.put("520400", new RegionInfo("贵州省", "安顺市"));
        REGION_CODE_MAP.put("520500", new RegionInfo("贵州省", "毕节市"));
        REGION_CODE_MAP.put("520600", new RegionInfo("贵州省", "铜仁市"));

        // 云南省
        REGION_CODE_MAP.put("530000", new RegionInfo("云南省", ""));
        REGION_CODE_MAP.put("530100", new RegionInfo("云南省", "昆明市"));
        REGION_CODE_MAP.put("530300", new RegionInfo("云南省", "曲靖市"));
        REGION_CODE_MAP.put("530400", new RegionInfo("云南省", "玉溪市"));
        REGION_CODE_MAP.put("530500", new RegionInfo("云南省", "保山市"));
        REGION_CODE_MAP.put("530600", new RegionInfo("云南省", "昭通市"));
        REGION_CODE_MAP.put("530700", new RegionInfo("云南省", "丽江市"));
        REGION_CODE_MAP.put("530800", new RegionInfo("云南省", "普洱市"));
        REGION_CODE_MAP.put("530900", new RegionInfo("云南省", "临沧市"));

        // 西藏自治区
        REGION_CODE_MAP.put("540000", new RegionInfo("西藏自治区", ""));
        REGION_CODE_MAP.put("540100", new RegionInfo("西藏自治区", "拉萨市"));
        REGION_CODE_MAP.put("540200", new RegionInfo("西藏自治区", "日喀则市"));
        REGION_CODE_MAP.put("540300", new RegionInfo("西藏自治区", "昌都市"));
        REGION_CODE_MAP.put("540400", new RegionInfo("西藏自治区", "林芝市"));
        REGION_CODE_MAP.put("540500", new RegionInfo("西藏自治区", "山南市"));

        // 陕西省
        REGION_CODE_MAP.put("610000", new RegionInfo("陕西省", ""));
        REGION_CODE_MAP.put("610100", new RegionInfo("陕西省", "西安市"));
        REGION_CODE_MAP.put("610200", new RegionInfo("陕西省", "铜川市"));
        REGION_CODE_MAP.put("610300", new RegionInfo("陕西省", "宝鸡市"));
        REGION_CODE_MAP.put("610400", new RegionInfo("陕西省", "咸阳市"));
        REGION_CODE_MAP.put("610500", new RegionInfo("陕西省", "渭南市"));
        REGION_CODE_MAP.put("610600", new RegionInfo("陕西省", "延安市"));
        REGION_CODE_MAP.put("610700", new RegionInfo("陕西省", "汉中市"));
        REGION_CODE_MAP.put("610800", new RegionInfo("陕西省", "榆林市"));
        REGION_CODE_MAP.put("610900", new RegionInfo("陕西省", "安康市"));
        REGION_CODE_MAP.put("611000", new RegionInfo("陕西省", "商洛市"));

        // 甘肃省
        REGION_CODE_MAP.put("620000", new RegionInfo("甘肃省", ""));
        REGION_CODE_MAP.put("620100", new RegionInfo("甘肃省", "兰州市"));
        REGION_CODE_MAP.put("620200", new RegionInfo("甘肃省", "嘉峪关市"));
        REGION_CODE_MAP.put("620300", new RegionInfo("甘肃省", "金昌市"));
        REGION_CODE_MAP.put("620400", new RegionInfo("甘肃省", "白银市"));
        REGION_CODE_MAP.put("620500", new RegionInfo("甘肃省", "天水市"));
        REGION_CODE_MAP.put("620600", new RegionInfo("甘肃省", "武威市"));
        REGION_CODE_MAP.put("620700", new RegionInfo("甘肃省", "张掖市"));
        REGION_CODE_MAP.put("620800", new RegionInfo("甘肃省", "平凉市"));
        REGION_CODE_MAP.put("620900", new RegionInfo("甘肃省", "酒泉市"));
        REGION_CODE_MAP.put("621000", new RegionInfo("甘肃省", "庆阳市"));
        REGION_CODE_MAP.put("621100", new RegionInfo("甘肃省", "定西市"));
        REGION_CODE_MAP.put("621200", new RegionInfo("甘肃省", "陇南市"));

        // 青海省
        REGION_CODE_MAP.put("630000", new RegionInfo("青海省", ""));
        REGION_CODE_MAP.put("630100", new RegionInfo("青海省", "西宁市"));
        REGION_CODE_MAP.put("630200", new RegionInfo("青海省", "海东市"));

        // 宁夏回族自治区
        REGION_CODE_MAP.put("640000", new RegionInfo("宁夏回族自治区", ""));
        REGION_CODE_MAP.put("640100", new RegionInfo("宁夏回族自治区", "银川市"));
        REGION_CODE_MAP.put("640200", new RegionInfo("宁夏回族自治区", "石嘴山市"));
        REGION_CODE_MAP.put("640300", new RegionInfo("宁夏回族自治区", "吴忠市"));
        REGION_CODE_MAP.put("640400", new RegionInfo("宁夏回族自治区", "固原市"));
        REGION_CODE_MAP.put("640500", new RegionInfo("宁夏回族自治区", "中卫市"));

        // 新疆维吾尔自治区
        REGION_CODE_MAP.put("650000", new RegionInfo("新疆维吾尔自治区", ""));
        REGION_CODE_MAP.put("650100", new RegionInfo("新疆维吾尔自治区", "乌鲁木齐市"));
        REGION_CODE_MAP.put("650200", new RegionInfo("新疆维吾尔自治区", "克拉玛依市"));
        REGION_CODE_MAP.put("650400", new RegionInfo("新疆维吾尔自治区", "吐鲁番市"));
        REGION_CODE_MAP.put("650500", new RegionInfo("新疆维吾尔自治区", "哈密市"));

        // 港澳台地区
        REGION_CODE_MAP.put("810000", new RegionInfo("香港特别行政区", "香港"));
        REGION_CODE_MAP.put("820000", new RegionInfo("澳门特别行政区", "澳门"));
        REGION_CODE_MAP.put("830000", new RegionInfo("台湾省", ""));
        REGION_CODE_MAP.put("710000", new RegionInfo("台湾省", ""));
    }
}
