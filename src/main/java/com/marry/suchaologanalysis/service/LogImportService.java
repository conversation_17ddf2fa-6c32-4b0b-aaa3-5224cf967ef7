package com.marry.suchaologanalysis.service;

import com.marry.suchaologanalysis.entity.LogRecord;
import com.marry.suchaologanalysis.mapper.LogRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.util.List;

/**
 * 日志导入服务
 */
@Service
@Slf4j
public class LogImportService {
    
    @Autowired
    private LogParseService logParseService;
    
    @Autowired
    private LogRecordMapper logRecordMapper;
    
    private static final int BATCH_SIZE = 1000; // 批量插入大小
    
    /**
     * 导入日志文件到数据库
     */
    @Transactional
    public void importLogFile(String filePath, String machineName) throws IOException {
        log.info("开始导入日志文件: {}, 机器名称: {}", filePath, machineName);
        
        // 解析日志文件
        List<LogRecord> records = logParseService.parseLogFile(filePath, machineName);
        
        if (records.isEmpty()) {
            log.warn("日志文件为空或解析失败: {}", filePath);
            return;
        }
        
        // 批量插入数据库
        int totalCount = records.size();
        int processedCount = 0;
        
        for (int i = 0; i < totalCount; i += BATCH_SIZE) {
            int endIndex = Math.min(i + BATCH_SIZE, totalCount);
            List<LogRecord> batch = records.subList(i, endIndex);
            
            try {
                logRecordMapper.batchInsert(batch);
                processedCount += batch.size();
                log.info("已处理 {}/{} 条记录", processedCount, totalCount);
            } catch (Exception e) {
                log.error("批量插入失败，跳过第{}到第{}条记录: {}", i + 1, endIndex, e.getMessage());
            }
        }
        
        log.info("日志文件导入完成: {}, 机器: {}, 成功导入{}条记录", filePath, machineName, processedCount);
    }
    
    /**
     * 导入多个日志文件（带机器名称映射）
     */
    public void importLogFilesWithMachines(List<LogFileInfo> logFileInfos) {
        for (LogFileInfo fileInfo : logFileInfos) {
            try {
                importLogFile(fileInfo.getFilePath(), fileInfo.getMachineName());
            } catch (Exception e) {
                log.error("导入日志文件失败: {}, 机器: {}, 错误: {}", 
                         fileInfo.getFilePath(), fileInfo.getMachineName(), e.getMessage());
            }
        }
    }
    
    /**
     * 清空日志表
     */
    @Transactional
    public void clearLogTable() {
        log.info("清空日志表");
        logRecordMapper.truncateTable();
    }
    
    /**
     * 获取日志总数
     */
    public Long getTotalLogCount() {
        return logRecordMapper.getTotalCount();
    }
    
    /**
     * 获取所有机器名称
     */
    public List<String> getAllMachineNames() {
        return logRecordMapper.getAllMachineNames();
    }
    
    /**
     * 日志文件信息类
     */
    public static class LogFileInfo {
        private String filePath;
        private String machineName;
        
        public LogFileInfo(String filePath, String machineName) {
            this.filePath = filePath;
            this.machineName = machineName;
        }
        
        public String getFilePath() {
            return filePath;
        }
        
        public String getMachineName() {
            return machineName;
        }
    }
}
