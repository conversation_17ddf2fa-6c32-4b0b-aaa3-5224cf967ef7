<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.marry.suchaologanalysis.mapper.LogRecordMapper">

    <!-- 结果映射 -->
    <resultMap id="LogRecordResultMap" type="com.marry.suchaologanalysis.entity.LogRecord">
        <id column="id" property="id"/>
        <result column="timestamp" property="timestamp"/>
        <result column="path" property="path"/>
        <result column="ajax" property="ajax"/>
        <result column="status" property="status"/>
        <result column="error_code" property="errorCode"/>
        <result column="error_msg" property="errorMsg"/>
        <result column="time" property="time"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="class_name" property="className"/>
        <result column="method" property="method"/>
        <result column="param" property="param"/>
        <result column="ip" property="ip"/>
        <result column="net_user_id" property="netUserId"/>
        <result column="machine_name" property="machineName"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <!-- 插入单条记录 -->
    <insert id="insert" parameterType="com.marry.suchaologanalysis.entity.LogRecord">
        INSERT INTO log_records (
            timestamp, path, ajax, status, error_code, error_msg, time,
            start_time, end_time, class_name, method, param, ip, net_user_id, machine_name
        ) VALUES (
            #{timestamp}, #{path}, #{ajax}, #{status}, #{errorCode}, #{errorMsg}, #{time},
            #{startTime}, #{endTime}, #{className}, #{method}, #{param}, #{ip}, #{netUserId}, #{machineName}
        )
    </insert>

    <!-- 批量插入 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO log_records (
            timestamp, path, ajax, status, error_code, error_msg, time,
            start_time, end_time, class_name, method, param, ip, net_user_id, machine_name
        ) VALUES
        <foreach collection="list" item="record" separator=",">
            (#{record.timestamp}, #{record.path}, #{record.ajax}, #{record.status}, #{record.errorCode},
             #{record.errorMsg}, #{record.time}, #{record.startTime}, #{record.endTime}, #{record.className},
             #{record.method}, #{record.param}, #{record.ip}, #{record.netUserId}, #{record.machineName})
        </foreach>
    </insert>

    <!-- 查询所有记录 -->
    <select id="findAll" resultMap="LogRecordResultMap">
        SELECT * FROM log_records ORDER BY timestamp DESC
    </select>

    <!-- 根据ID查询 -->
    <select id="findById" parameterType="long" resultMap="LogRecordResultMap">
        SELECT * FROM log_records WHERE id = #{id}
    </select>

    <!-- 根据时间范围查询 -->
    <select id="findByTimeRange" resultMap="LogRecordResultMap">
        SELECT * FROM log_records
        WHERE timestamp BETWEEN #{startTime} AND #{endTime}
        <if test="machineName != null and machineName != ''">
            AND machine_name = #{machineName}
        </if>
        ORDER BY timestamp DESC
    </select>

    <!-- 统计总数 -->
    <select id="count" resultType="long">
        SELECT COUNT(*) FROM log_records
        <where>
            <if test="machineName != null and machineName != ''">
                machine_name = #{machineName}
            </if>
        </where>
    </select>

    <!-- 清空所有数据 -->
    <delete id="deleteAll">
        DELETE FROM log_records
    </delete>

    <!-- 按小时统计 -->
    <select id="getHourlyStatistics" resultType="map">
        SELECT
            FORMATDATETIME(timestamp, 'yyyy-MM-dd HH') as hour,
            COUNT(*) as count,
            AVG(time) as avg_time,
            MAX(time) as max_time,
            MIN(time) as min_time
        FROM log_records
        WHERE timestamp BETWEEN #{startTime} AND #{endTime}
        <if test="machineName != null and machineName != ''">
            AND machine_name = #{machineName}
        </if>
        GROUP BY FORMATDATETIME(timestamp, 'yyyy-MM-dd HH')
        ORDER BY hour
    </select>

    <!-- 按天统计 -->
    <select id="getDailyStatistics" resultType="map">
        SELECT
            FORMATDATETIME(timestamp, 'yyyy-MM-dd') as day,
            COUNT(*) as count,
            AVG(time) as avg_time,
            MAX(time) as max_time,
            MIN(time) as min_time
        FROM log_records
        WHERE timestamp BETWEEN #{startTime} AND #{endTime}
        <if test="machineName != null and machineName != ''">
            AND machine_name = #{machineName}
        </if>
        GROUP BY FORMATDATETIME(timestamp, 'yyyy-MM-dd')
        ORDER BY day
    </select>

    <!-- 错误统计 -->
    <select id="getErrorStatistics" resultType="map">
        SELECT
            error_code,
            COUNT(*) as count
        FROM log_records
        WHERE status = 0
        AND timestamp BETWEEN #{startTime} AND #{endTime}
        <if test="machineName != null and machineName != ''">
            AND machine_name = #{machineName}
        </if>
        GROUP BY error_code
        ORDER BY count DESC
    </select>

    <!-- 响应时间分布统计 -->
    <select id="getResponseTimeDistribution" resultType="map">
        SELECT
            CASE
                WHEN time &lt; 100 THEN '&lt;100ms'
                WHEN time &lt; 500 THEN '100-500ms'
                WHEN time &lt; 1000 THEN '500ms-1s'
                WHEN time &lt; 5000 THEN '1-5s'
                ELSE '&gt;5s'
            END as time_range,
            COUNT(*) as count
        FROM log_records
        WHERE timestamp BETWEEN #{startTime} AND #{endTime}
        <if test="machineName != null and machineName != ''">
            AND machine_name = #{machineName}
        </if>
        GROUP BY
            CASE
                WHEN time &lt; 100 THEN '&lt;100ms'
                WHEN time &lt; 500 THEN '100-500ms'
                WHEN time &lt; 1000 THEN '500ms-1s'
                WHEN time &lt; 5000 THEN '1-5s'
                ELSE '&gt;5s'
            END
        ORDER BY
            CASE
                WHEN time &lt; 100 THEN 1
                WHEN time &lt; 500 THEN 2
                WHEN time &lt; 1000 THEN 3
                WHEN time &lt; 5000 THEN 4
                ELSE 5
            END
    </select>

    <!-- 按路径统计 -->
    <select id="getPathStatistics" resultType="map">
        SELECT
            path,
            COUNT(*) as count,
            AVG(time) as avg_time,
            MAX(time) as max_time,
            MIN(time) as min_time,
            SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as success_count,
            SUM(CASE WHEN status != 1 THEN 1 ELSE 0 END) as error_count
        FROM log_records
        WHERE timestamp BETWEEN #{startTime} AND #{endTime}
        AND path IS NOT NULL
        <if test="machineName != null and machineName != ''">
            AND machine_name = #{machineName}
        </if>
        GROUP BY path
        ORDER BY count DESC
        LIMIT 20
    </select>

    <!-- 按路径统计（指定机器） -->
    <select id="getPathStatisticsByMachine" resultType="map">
        SELECT
            path,
            COUNT(*) as count,
            AVG(time) as avg_time,
            MAX(time) as max_time,
            MIN(time) as min_time,
            SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as success_count,
            SUM(CASE WHEN status != 1 THEN 1 ELSE 0 END) as error_count
        FROM log_records
        WHERE timestamp BETWEEN #{startTime} AND #{endTime}
        AND machine_name = #{machineName}
        AND path IS NOT NULL
        GROUP BY path
        ORDER BY count DESC
        LIMIT 20
    </select>

    <!-- 获取所有路径列表 -->
    <select id="getAllPaths" resultType="java.lang.String">
        SELECT DISTINCT path
        FROM log_records
        WHERE path IS NOT NULL
        ORDER BY path
    </select>

</mapper>
