package com.marry.suchaologanalysis.integration;

import com.marry.suchaologanalysis.entity.LogRecord;
import com.marry.suchaologanalysis.mapper.LogRecordMapper;
import com.marry.suchaologanalysis.service.LogImportService;
import com.marry.suchaologanalysis.service.LogParseService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.jdbc.Sql;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@ActiveProfiles("test")
@Sql(scripts = "/test-schema.sql", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
public class LogImportIntegrationTest {

    @Autowired
    private LogImportService logImportService;

    @Autowired
    private LogParseService logParseService;

    @Autowired
    private LogRecordMapper logRecordMapper;

    @Test
    public void testLogImportFlow() throws IOException {
        List<LogImportService.LogFileInfo> paths = new ArrayList<>();
        paths.add(new LogImportService.LogFileInfo("/Users/<USER>/Downloads/other-host-logs/api-1/request.log", "venue-api-1"));
        paths.add(new LogImportService.LogFileInfo("/Users/<USER>/Downloads/other-host-logs/api-1/request.log", "venue-api-1"));
        logImportService.importLogFilesWithMachines(paths);

    }

    @Test
    public void testLogParseService() {
        String logLine = "[2025-07-01 15:02:23.276] {\"path\":\"/api/parse-test\",\"ajax\":true,\"status\":1,\"errorCode\":\"\",\"errorMsg\":\"\",\"time\":156,\"startTime\":\"2025-07-01 15:02:23.120\",\"endTime\":\"2025-07-01 15:02:23.276\",\"className\":\"com.test.ParseController\",\"method\":\"parseMethod\",\"param\":{\"ip\":\"*************\",\"netUserId\":99999,\"customData\":\"test\"}}";

        LogRecord record = logParseService.parseLogLine(logLine, "parse-test-machine");

        assertNotNull(record);
        assertEquals("/api/parse-test", record.getPath());
        assertTrue(record.getAjax());
        assertEquals(1, record.getStatus());
        assertEquals(156L, record.getTime());
        assertEquals("com.test.ParseController", record.getClassName());
        assertEquals("parseMethod", record.getMethod());
        assertEquals("*************", record.getIp());
        assertEquals(99999L, record.getNetUserId());
        assertEquals("parse-test-machine", record.getMachineName());

        // 验证时间戳解析
        LocalDateTime expectedTimestamp = LocalDateTime.of(2025, 7, 1, 15, 2, 23, 276000000);
        assertEquals(expectedTimestamp, record.getTimestamp());

        // 验证param JSON存储
        assertNotNull(record.getParam());
        assertTrue(record.getParam().contains("customData"));
        assertTrue(record.getParam().contains("test"));
    }
}
